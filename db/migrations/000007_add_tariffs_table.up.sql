-- Миграция: создание таблицы тарифов для управления ценами через админ-меню
CREATE TABLE tariffs (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR(32) NOT NULL UNIQUE, -- Уникальный код тарифа (например, '1m', '3m')
    title VARCHAR(64) NOT NULL,       -- Название тарифа для отображения
    price_rub INTEGER NOT NULL,       -- <PERSON><PERSON><PERSON> в рублях
    price_stars INTEGER NOT NULL,     -- <PERSON><PERSON><PERSON> в Telegram Stars
    active BOOLEAN NOT NULL DEFAULT TRUE, -- А<PERSON><PERSON><PERSON>в<PERSON><PERSON> ли тариф
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
-- Индекс для быстрого поиска активных тарифов
CREATE INDEX idx_tariffs_active ON tariffs(active); 