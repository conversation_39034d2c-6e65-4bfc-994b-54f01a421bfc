-- Откат миграции: удаление таблиц промокодов

-- Удаляем триггеры
DROP TRIGGER IF EXISTS trigger_update_promocode_activations ON promocode_usages;
DROP TRIGGER IF EXISTS trigger_calculate_promocode_expires_at ON promocodes;
DROP TRIGGER IF EXISTS trigger_update_promocodes_updated_at ON promocodes;

-- Удаляем функции
DROP FUNCTION IF EXISTS update_promocode_activations();
DROP FUNCTION IF EXISTS calculate_promocode_expires_at();
DROP FUNCTION IF EXISTS update_promocodes_updated_at();

-- Удаляем индексы
DROP INDEX IF EXISTS idx_promocode_usages_used_at;
DROP INDEX IF EXISTS idx_promocode_usages_customer_id;
DROP INDEX IF EXISTS idx_promocode_usages_promocode_id;

DROP INDEX IF EXISTS idx_promocodes_user_type;
DROP INDEX IF EXISTS idx_promocodes_code;
DROP INDEX IF EXISTS idx_promocodes_expires_at;
DROP INDEX IF EXISTS idx_promocodes_active;

-- Удаляем таблицы
DROP TABLE IF EXISTS promocode_usages;
DROP TABLE IF EXISTS promocodes;
