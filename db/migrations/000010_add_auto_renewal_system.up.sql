-- Миграция: создание системы автопродления подписок
-- Добавляет таблицы для управления автопродлением и сохранения платежных данных

-- Таблица настроек автопродления для пользователей
CREATE TABLE auto_renewal_settings (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL REFERENCES customer(id) ON DELETE CASCADE,
    enabled BOOLEAN NOT NULL DEFAULT false,
    payment_method VARCHAR(50) NOT NULL, -- 'yookasa', 'cryptopay', 'telegram', 'tribute'
    saved_payment_data JSONB, -- Сохраненные данные для автоплатежей (токены карт, payment_method_id и т.д.)
    tariff_code VARCHAR(32) NOT NULL, -- Код тарифа для автопродления
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Ограничения
    UNIQUE(customer_id), -- Один пользователь - одна настройка автопродления
    CONSTRAINT auto_renewal_payment_method_check CHECK (payment_method IN ('yookasa', 'cryptopay', 'telegram', 'tribute'))
);

-- Таблица истории автопродлений
CREATE TABLE auto_renewal_history (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL REFERENCES customer(id) ON DELETE CASCADE,
    purchase_id BIGINT REFERENCES purchase(id) ON DELETE SET NULL, -- Может быть NULL если покупка не была создана
    auto_renewal_settings_id BIGINT NOT NULL REFERENCES auto_renewal_settings(id) ON DELETE CASCADE,
    
    -- Статус операции
    status VARCHAR(50) NOT NULL, -- 'success', 'failed', 'cancelled', 'pending'
    error_message TEXT, -- Сообщение об ошибке при неудачном автопродлении
    
    -- Детали операции
    amount DECIMAL(20, 8), -- Сумма списания
    currency VARCHAR(10), -- Валюта
    payment_method VARCHAR(50), -- Способ оплаты, который использовался
    tariff_code VARCHAR(32), -- Код тарифа
    
    -- Временные метки
    processed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Ограничения
    CONSTRAINT auto_renewal_history_status_check CHECK (status IN ('success', 'failed', 'cancelled', 'pending'))
);

-- Таблица уведомлений об автопродлении
CREATE TABLE auto_renewal_notifications (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL REFERENCES customer(id) ON DELETE CASCADE,
    notification_type VARCHAR(20) NOT NULL, -- '24h', '12h', 'failed', 'success'
    sent_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    subscription_expires_at TIMESTAMP WITH TIME ZONE NOT NULL, -- Дата окончания подписки, для которой отправлено уведомление
    
    -- Ограничения
    CONSTRAINT auto_renewal_notifications_type_check CHECK (notification_type IN ('24h', '12h', 'failed', 'success')),
    -- Уникальность: один тип уведомления на одну дату окончания подписки
    UNIQUE(customer_id, notification_type, subscription_expires_at)
);

-- Индексы для оптимизации запросов
CREATE INDEX idx_auto_renewal_settings_customer_id ON auto_renewal_settings(customer_id);
CREATE INDEX idx_auto_renewal_settings_enabled ON auto_renewal_settings(enabled) WHERE enabled = true;

CREATE INDEX idx_auto_renewal_history_customer_id ON auto_renewal_history(customer_id);
CREATE INDEX idx_auto_renewal_history_status ON auto_renewal_history(status);
CREATE INDEX idx_auto_renewal_history_processed_at ON auto_renewal_history(processed_at);

CREATE INDEX idx_auto_renewal_notifications_customer_id ON auto_renewal_notifications(customer_id);
CREATE INDEX idx_auto_renewal_notifications_type ON auto_renewal_notifications(notification_type);
CREATE INDEX idx_auto_renewal_notifications_expires_at ON auto_renewal_notifications(subscription_expires_at);

-- Функция для автоматического обновления updated_at в auto_renewal_settings
CREATE OR REPLACE FUNCTION update_auto_renewal_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Триггер для автоматического обновления updated_at
CREATE TRIGGER trigger_update_auto_renewal_settings_updated_at
    BEFORE UPDATE ON auto_renewal_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_auto_renewal_settings_updated_at();

-- Функция для очистки старых уведомлений (старше 30 дней)
CREATE OR REPLACE FUNCTION cleanup_old_auto_renewal_notifications()
RETURNS void AS $$
BEGIN
    DELETE FROM auto_renewal_notifications 
    WHERE sent_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Комментарии к таблицам
COMMENT ON TABLE auto_renewal_settings IS 'Настройки автопродления подписок для пользователей';
COMMENT ON TABLE auto_renewal_history IS 'История операций автопродления подписок';
COMMENT ON TABLE auto_renewal_notifications IS 'Журнал отправленных уведомлений об автопродлении';

COMMENT ON COLUMN auto_renewal_settings.saved_payment_data IS 'JSON с сохраненными данными для автоплатежей (payment_method_id для Yookasa, настройки для других систем)';
COMMENT ON COLUMN auto_renewal_history.error_message IS 'Детальное описание ошибки при неудачном автопродлении';
COMMENT ON COLUMN auto_renewal_notifications.subscription_expires_at IS 'Дата окончания подписки, для предотвращения дублирования уведомлений';
