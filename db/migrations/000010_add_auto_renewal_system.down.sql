-- Откат миграции: удаление системы автопродления подписок

-- Удаляем триггеры
DROP TRIGGER IF EXISTS trigger_update_auto_renewal_settings_updated_at ON auto_renewal_settings;

-- Удаляем функции
DROP FUNCTION IF EXISTS cleanup_old_auto_renewal_notifications();
DROP FUNCTION IF EXISTS update_auto_renewal_settings_updated_at();

-- Удаляем индексы
DROP INDEX IF EXISTS idx_auto_renewal_notifications_expires_at;
DROP INDEX IF EXISTS idx_auto_renewal_notifications_type;
DROP INDEX IF EXISTS idx_auto_renewal_notifications_customer_id;

DROP INDEX IF EXISTS idx_auto_renewal_history_processed_at;
DROP INDEX IF EXISTS idx_auto_renewal_history_status;
DROP INDEX IF EXISTS idx_auto_renewal_history_customer_id;

DROP INDEX IF EXISTS idx_auto_renewal_settings_enabled;
DROP INDEX IF EXISTS idx_auto_renewal_settings_customer_id;

-- Удаляем таблицы (в обратном порядке зависимостей)
DROP TABLE IF EXISTS auto_renewal_notifications;
DROP TABLE IF EXISTS auto_renewal_history;
DROP TABLE IF EXISTS auto_renewal_settings;
