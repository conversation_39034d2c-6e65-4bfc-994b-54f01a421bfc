-- Миграция: создание таблиц для системы промокодов

-- Таблица промокодов
CREATE TABLE promocodes (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE, -- Уникальный код промокода (цифры и английские буквы)
    description TEXT NOT NULL,        -- Описание промокода
    discount_percent INTEGER NOT NULL CHECK (discount_percent > 0 AND discount_percent <= 100), -- Процент скидки (1-100)
    
    -- Срок действия
    expiry_type VARCHAR(20) NOT NULL CHECK (expiry_type IN ('hours', 'days', 'end_of_day')), -- Тип срока действия
    expiry_value INTEGER,             -- Значение для hours/days (NULL для end_of_day)
    expires_at TIMESTAMP WITH TIME ZONE, -- Вычисленное время истечения
    
    -- Лимиты активации
    activation_limit INTEGER NOT NULL DEFAULT 0, -- 0 = бесконечно, >0 = конкретное количество
    current_activations INTEGER NOT NULL DEFAULT 0, -- Текущее количество активаций
    
    -- Ограничения по пользователям
    user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('with_subscription', 'without_subscription', 'all')), -- Тип пользователей
    
    -- Применимые тарифы (JSON массив кодов тарифов или "all")
    applicable_tariffs JSONB NOT NULL DEFAULT '"all"'::jsonb, -- "all" или ["1m", "3m", ...]
    
    -- Статус
    active BOOLEAN NOT NULL DEFAULT TRUE, -- Активен ли промокод
    
    -- Метаданные
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_by BIGINT, -- ID администратора, создавшего промокод
    
    -- Индексы
    CONSTRAINT promocodes_code_format CHECK (code ~ '^[A-Za-z0-9]+$') -- Только цифры и английские буквы
);

-- Таблица использований промокодов
CREATE TABLE promocode_usages (
    id BIGSERIAL PRIMARY KEY,
    promocode_id BIGINT NOT NULL REFERENCES promocodes(id) ON DELETE CASCADE,
    customer_id BIGINT NOT NULL REFERENCES customer(id) ON DELETE CASCADE,
    purchase_id BIGINT NOT NULL REFERENCES purchase(id) ON DELETE CASCADE,
    tariff_code VARCHAR(32) NOT NULL, -- Код тарифа, к которому применен промокод
    
    -- Детали применения
    original_price INTEGER NOT NULL,  -- Оригинальная цена
    discount_amount INTEGER NOT NULL, -- Размер скидки
    final_price INTEGER NOT NULL,     -- Итоговая цена
    currency VARCHAR(10) NOT NULL,    -- Валюта (RUB/STARS)
    
    -- Метаданные
    used_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Ограничения
    UNIQUE(promocode_id, customer_id) -- Один промокод - один раз на пользователя
);

-- Индексы для оптимизации запросов
CREATE INDEX idx_promocodes_active ON promocodes(active) WHERE active = true;
CREATE INDEX idx_promocodes_expires_at ON promocodes(expires_at) WHERE active = true;
CREATE INDEX idx_promocodes_code ON promocodes(code) WHERE active = true;
CREATE INDEX idx_promocodes_user_type ON promocodes(user_type) WHERE active = true;

CREATE INDEX idx_promocode_usages_promocode_id ON promocode_usages(promocode_id);
CREATE INDEX idx_promocode_usages_customer_id ON promocode_usages(customer_id);
CREATE INDEX idx_promocode_usages_used_at ON promocode_usages(used_at);

-- Функция для автоматического обновления updated_at
CREATE OR REPLACE FUNCTION update_promocodes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Триггер для автоматического обновления updated_at
CREATE TRIGGER trigger_update_promocodes_updated_at
    BEFORE UPDATE ON promocodes
    FOR EACH ROW
    EXECUTE FUNCTION update_promocodes_updated_at();

-- Функция для автоматического расчета expires_at при вставке/обновлении
CREATE OR REPLACE FUNCTION calculate_promocode_expires_at()
RETURNS TRIGGER AS $$
BEGIN
    CASE NEW.expiry_type
        WHEN 'hours' THEN
            NEW.expires_at = NOW() + (NEW.expiry_value || ' hours')::INTERVAL;
        WHEN 'days' THEN
            NEW.expires_at = NOW() + (NEW.expiry_value || ' days')::INTERVAL;
        WHEN 'end_of_day' THEN
            -- До конца дня по UTC+03 (московское время)
            NEW.expires_at = (DATE(NOW() AT TIME ZONE 'Europe/Moscow') + INTERVAL '1 day' - INTERVAL '1 second') AT TIME ZONE 'Europe/Moscow';
        ELSE
            RAISE EXCEPTION 'Invalid expiry_type: %', NEW.expiry_type;
    END CASE;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Триггер для автоматического расчета expires_at
CREATE TRIGGER trigger_calculate_promocode_expires_at
    BEFORE INSERT OR UPDATE ON promocodes
    FOR EACH ROW
    EXECUTE FUNCTION calculate_promocode_expires_at();

-- Функция для обновления счетчика активаций
CREATE OR REPLACE FUNCTION update_promocode_activations()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Увеличиваем счетчик при добавлении использования
        UPDATE promocodes 
        SET current_activations = current_activations + 1
        WHERE id = NEW.promocode_id;
        
        -- Деактивируем промокод, если достигнут лимит
        UPDATE promocodes 
        SET active = false
        WHERE id = NEW.promocode_id 
          AND activation_limit > 0 
          AND current_activations >= activation_limit;
          
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Уменьшаем счетчик при удалении использования
        UPDATE promocodes 
        SET current_activations = current_activations - 1
        WHERE id = OLD.promocode_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Триггер для обновления счетчика активаций
CREATE TRIGGER trigger_update_promocode_activations
    AFTER INSERT OR DELETE ON promocode_usages
    FOR EACH ROW
    EXECUTE FUNCTION update_promocode_activations();
