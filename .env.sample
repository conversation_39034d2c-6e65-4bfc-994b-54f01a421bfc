PRICE_1=199
PRICE_3=499
PRICE_6=899
PRICE_12=1699
STARS_PRICE_1=119
STARS_PRICE_3=289
STARS_PRICE_6=509
STARS_PRICE_12=959

TELEGRAM_TOKEN=token

REFERRAL_DAYS=7

MINI_APP_URL=

DATABASE_URL=************************************/postgres?sslmode=disable

POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=postgres

REMNAWAVE_URL=https://example.com
REMNAWAVE_MODE=remote
REMNAWAVE_TOKEN=token
REMNAWAVE_SECRET_KEY_NAME=NAME
REMNAWAVE_SECRET_KEY_VALUE=VALUE

CRYPTO_PAY_ENABLED=true
CRYPTO_PAY_TOKEN=token
CRYPTO_PAY_URL=https://pay.crypt.bot

YOOKASA_ENABLED=true
YOOKASA_SECRET_KEY=key
YOOKASA_SHOP_ID=id
YOOKASA_URL=https://api.yookassa.ru/v3
YOOKASA_EMAIL=<EMAIL>

TRAFFIC_LIMIT=100

TELEGRAM_STARS_ENABLED=true

TRIAL_TRAFFIC_LIMIT=20
TRIAL_DAYS=2

ADMIN_TELEGRAM_ID=123123123

SERVER_STATUS_URL="https://example.com/status"
SUPPORT_URL="https://example.com/support"
FEEDBACK_URL="https://example.com/feedback"
CHANNEL_URL="https://t.me/examplechannel"

# Список UUID инбаундов для назначения пользователям, разделенный запятыми
# Например: 773db654-a8b2-413a-a50b-75c3536238fd,bc979bdd-f1fa-4d94-8a51-38a0f518a2a2
INBOUND_UUIDS=