name: Build docker image

on:
  push:
    tags:
      - '*'
  workflow_dispatch:

jobs:
  build-docker-image:
    runs-on: self-hosted
    permissions:
      packages: write
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.PERSONAL_ACCESS_TOKEN }}

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository_owner }}/remnawave-telegram-shop-bot
          # Set up proper tagging strategy
          tags: |
            # Always include latest tag
            type=raw,value=latest,enable=${{ github.ref_type == 'tag' }}
            # Use semver tags when Git tag is in semver format
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            # Use the short SHA for non-tag builds
            type=sha,format=short,prefix=
            # Include the tag name if it's a tag-triggered build
            type=ref,event=tag

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            VERSION=${{ steps.meta.outputs.version }}
            COMMIT=${{ github.sha }}
            GITHUB_REPOSITORY=${{ github.repository }}