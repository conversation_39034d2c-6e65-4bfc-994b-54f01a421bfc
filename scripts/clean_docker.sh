#!/bin/bash
# Скрипт для полной очистки Docker: контейнеры, образы, тома, сети

# Остановить все контейнеры
if [ $(docker ps -aq | wc -l) -gt 0 ]; then
  docker stop $(docker ps -aq)
fi

# Удалить все контейнеры
if [ $(docker ps -aq | wc -l) -gt 0 ]; then
  docker rm $(docker ps -aq)
fi

# Удалить все образы
if [ $(docker images -q | wc -l) -gt 0 ]; then
  docker rmi -f $(docker images -q)
fi

# Удалить все тома
if [ $(docker volume ls -q | wc -l) -gt 0 ]; then
  docker volume rm $(docker volume ls -q)
fi

# Удалить все пользовательские сети (кроме default, bridge, host, none)
for net in $(docker network ls --filter "type=custom" -q); do
  docker network rm $net
  echo "Удалена сеть $net"
done

echo "Docker полностью очищен." 