package notification

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/service"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// PromoCodeNotificationService - сервис уведомлений о промокодах
type PromoCodeNotificationService struct {
	promoCodeService   *service.PromoCodeService
	customerRepository *database.CustomerRepository
	promoCodeUsageRepo *database.PromoCodeUsageRepository
	tariffRepository   *database.TariffRepository
	telegramBot        *bot.Bot
}

// NewPromoCodeNotificationService создает новый сервис уведомлений о промокодах
func NewPromoCodeNotificationService(
	promoCodeService *service.PromoCodeService,
	customerRepository *database.CustomerRepository,
	promoCodeUsageRepo *database.PromoCodeUsageRepository,
	tariffRepository *database.TariffRepository,
	telegramBot *bot.Bot,
) *PromoCodeNotificationService {
	return &PromoCodeNotificationService{
		promoCodeService:   promoCodeService,
		customerRepository: customerRepository,
		promoCodeUsageRepo: promoCodeUsageRepo,
		tariffRepository:   tariffRepository,
		telegramBot:        telegramBot,
	}
}

// SendExpiringPromoCodeNotifications отправляет уведомления о промокодах, которые истекают
func (s *PromoCodeNotificationService) SendExpiringPromoCodeNotifications(ctx context.Context) error {
	// Получаем промокоды, которые истекают в течение часа
	expiringPromoCodes, err := s.promoCodeService.GetExpiringPromoCodes(ctx, time.Hour)
	if err != nil {
		return fmt.Errorf("failed to get expiring promo codes: %w", err)
	}

	if len(expiringPromoCodes) == 0 {
		slog.Info("No expiring promo codes found")
		return nil
	}

	slog.Info(fmt.Sprintf("Found %d expiring promo codes", len(expiringPromoCodes)))

	// Отправляем уведомления пользователям и администратору
	for _, promoCode := range expiringPromoCodes {
		// Отправляем уведомления пользователям
		err := s.sendUserNotifications(ctx, promoCode)
		if err != nil {
			slog.Error("Failed to send user notifications for promo code",
				"promo_code", promoCode.Code,
				"error", err)
		}

		// Отправляем уведомление администратору
		adminID := config.GetAdminTelegramId()
		if adminID != 0 {
			err := s.sendAdminNotification(ctx, adminID, promoCode)
			if err != nil {
				slog.Error("Failed to send admin promo code expiry notification",
					"promo_code", promoCode.Code,
					"expires_at", promoCode.ExpiresAt,
					"error", err)
			} else {
				slog.Info("Admin promo code expiry notification sent successfully",
					"promo_code", promoCode.Code,
					"expires_at", promoCode.ExpiresAt)
			}
		}
	}

	return nil
}

// sendUserNotifications отправляет уведомления пользователям о истекающем промокоде
func (s *PromoCodeNotificationService) sendUserNotifications(ctx context.Context, promoCode database.PromoCode) error {
	// Получаем всех пользователей в зависимости от типа промокода
	var customers []database.Customer
	var err error

	switch promoCode.UserType {
	case database.PromoCodeUserTypeWithSubscription:
		// Получаем всех пользователей и фильтруем тех, у кого есть активная подписка
		allCustomers, err := s.customerRepository.GetAll(ctx)
		if err != nil {
			return fmt.Errorf("ошибка получения всех пользователей: %w", err)
		}
		for _, customer := range allCustomers {
			if customer.ExpireAt != nil && customer.ExpireAt.After(time.Now()) {
				customers = append(customers, customer)
			}
		}
	case database.PromoCodeUserTypeWithoutSubscription:
		customers, err = s.customerRepository.GetUsersWithoutActiveSubscription(ctx)
	case database.PromoCodeUserTypeAll:
		customers, err = s.customerRepository.GetAll(ctx)
	default:
		return fmt.Errorf("неизвестный тип пользователей промокода: %s", promoCode.UserType)
	}

	if err != nil {
		return fmt.Errorf("ошибка получения пользователей: %w", err)
	}

	// Фильтруем пользователей, которые еще не использовали этот промокод
	var eligibleCustomers []database.Customer
	for _, customer := range customers {
		hasUsed, err := s.promoCodeUsageRepo.HasUsedPromoCode(ctx, promoCode.ID, customer.ID)
		if err != nil {
			slog.Error("Ошибка проверки использования промокода", "customer_id", customer.ID, "promo_code_id", promoCode.ID, "error", err)
			continue
		}
		if !hasUsed {
			eligibleCustomers = append(eligibleCustomers, customer)
		}
	}

	if len(eligibleCustomers) == 0 {
		slog.Info("Нет подходящих пользователей для уведомления о промокоде", "promo_code", promoCode.Code)
		return nil
	}

	slog.Info(fmt.Sprintf("Отправляем уведомления о промокоде %s для %d пользователей", promoCode.Code, len(eligibleCustomers)))

	// Отправляем уведомления
	successCount := 0
	for _, customer := range eligibleCustomers {
		err := s.sendUserNotification(ctx, customer.TelegramID, promoCode)
		if err != nil {
			slog.Error("Ошибка отправки уведомления пользователю", "customer_id", customer.ID, "telegram_id", customer.TelegramID, "error", err)
			continue
		}
		successCount++
	}

	slog.Info(fmt.Sprintf("Успешно отправлено %d уведомлений о промокоде %s", successCount, promoCode.Code))
	return nil
}

// sendUserNotification отправляет уведомление конкретному пользователю
func (s *PromoCodeNotificationService) sendUserNotification(ctx context.Context, userID int64, promoCode database.PromoCode) error {
	// Рассчитываем время до истечения
	timeUntilExpiry := time.Until(promoCode.ExpiresAt)

	var timeText string
	if timeUntilExpiry < time.Hour {
		minutes := int(timeUntilExpiry.Minutes())
		if minutes <= 0 {
			timeText = "менее минуты"
		} else {
			timeText = fmt.Sprintf("%d мин.", minutes)
		}
	} else {
		hours := int(timeUntilExpiry.Hours())
		timeText = fmt.Sprintf("%d ч.", hours)
	}

	// Определяем применимые тарифы
	var tariffsText string
	if promoCode.ApplicableTariffs.All {
		tariffsText = "все тарифы"
	} else {
		// Получаем конкретные тарифы
		if len(promoCode.ApplicableTariffs.Codes) > 0 {
			tariffNames := make([]string, 0, len(promoCode.ApplicableTariffs.Codes))
			for _, code := range promoCode.ApplicableTariffs.Codes {
				tariff, err := s.tariffRepository.GetByCode(ctx, code)
				if err == nil && tariff != nil {
					tariffNames = append(tariffNames, tariff.Title)
				}
			}
			if len(tariffNames) > 0 {
				tariffsText = fmt.Sprintf("%v", tariffNames)
			} else {
				tariffsText = "выбранные тарифы"
			}
		} else {
			tariffsText = "все тарифы"
		}
	}

	// Формируем текст уведомления для пользователя
	messageText := fmt.Sprintf(
		"🎟️ <b>Ваш промокод истекает!</b>\n\n"+
			"Промокод <b>%s</b> истекает через <b>%s</b>\n\n"+
			"📝 Применим к: %s\n"+
			"💰 Скидка: <b>%d%%</b>\n"+
			"⏰ Истекает: %s\n\n"+
			"Успейте воспользоваться скидкой!",
		promoCode.Code,
		timeText,
		tariffsText,
		promoCode.DiscountPercent,
		promoCode.ExpiresAt.Format("02.01.2006 15:04"),
	)

	// Отправляем уведомление
	_, err := s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:    userID,
		Text:      messageText,
		ParseMode: models.ParseModeHTML,
	})

	return err
}

// sendAdminNotification отправляет уведомление администратору об истечении промокода
func (s *PromoCodeNotificationService) sendAdminNotification(ctx context.Context, adminID int64, promoCode database.PromoCode) error {
	// Рассчитываем время до истечения
	timeUntilExpiry := time.Until(promoCode.ExpiresAt)

	var timeText string
	if timeUntilExpiry < time.Hour {
		minutes := int(timeUntilExpiry.Minutes())
		if minutes <= 0 {
			timeText = "менее минуты"
		} else {
			timeText = fmt.Sprintf("%d мин.", minutes)
		}
	} else {
		hours := int(timeUntilExpiry.Hours())
		timeText = fmt.Sprintf("%d ч.", hours)
	}

	// Получаем статистику использования
	stats, err := s.promoCodeService.GetPromoCodeStatistics(ctx, promoCode.ID)
	if err != nil {
		slog.Error("Failed to get promo code statistics", "promo_code", promoCode.Code, "error", err)
		stats = map[string]interface{}{
			"total_usages": 0,
		}
	}

	totalUsages, _ := stats["total_usages"].(int)

	// Формируем текст уведомления для администратора
	messageText := fmt.Sprintf(
		"⚠️ <b>Уведомление о промокоде</b> ⚠️\n\n"+
			"Промокод <b>%s</b> истекает через <b>%s</b>\n\n"+
			"📝 Описание: %s\n"+
			"💰 Скидка: <b>%d%%</b>\n"+
			"📊 Использований: <b>%d</b>\n"+
			"⏰ Истекает: %s\n\n"+
			"Рассмотрите возможность продления срока действия промокода.",
		promoCode.Code,
		timeText,
		promoCode.Description,
		promoCode.DiscountPercent,
		totalUsages,
		promoCode.ExpiresAt.Format("02.01.2006 15:04"),
	)

	// Отправляем уведомление
	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:    adminID,
		Text:      messageText,
		ParseMode: models.ParseModeHTML,
	})

	return err
}

// SendDailyPromoCodeReport отправляет ежедневный отчет по промокодам
func (s *PromoCodeNotificationService) SendDailyPromoCodeReport(ctx context.Context) error {
	adminID := config.GetAdminTelegramId()
	if adminID == 0 {
		slog.Warn("Admin Telegram ID not configured, skipping daily promo code report")
		return nil
	}

	// Получаем все активные промокоды
	activePromoCodes, err := s.promoCodeService.GetAllPromoCodes(ctx, true)
	if err != nil {
		return fmt.Errorf("failed to get active promo codes: %w", err)
	}

	if len(activePromoCodes) == 0 {
		return nil // Не отправляем отчет, если нет активных промокодов
	}

	// Подсчитываем статистику
	var totalActive, expiredCount, limitReachedCount int
	var totalUsages int

	for _, promoCode := range activePromoCodes {
		totalActive++

		if promoCode.IsExpired() {
			expiredCount++
		} else if promoCode.IsLimitReached() {
			limitReachedCount++
		}

		// Получаем статистику использования
		stats, err := s.promoCodeService.GetPromoCodeStatistics(ctx, promoCode.ID)
		if err == nil {
			if usages, ok := stats["total_usages"].(int); ok {
				totalUsages += usages
			}
		}
	}

	// Формируем отчет
	messageText := fmt.Sprintf(
		"📊 <b>Ежедневный отчет по промокодам</b>\n\n"+
			"🎟️ Всего активных: <b>%d</b>\n"+
			"⏰ Истекших: <b>%d</b>\n"+
			"🚫 Лимит исчерпан: <b>%d</b>\n"+
			"📈 Всего использований: <b>%d</b>\n\n"+
			"Дата: %s",
		totalActive,
		expiredCount,
		limitReachedCount,
		totalUsages,
		time.Now().Format("02.01.2006"),
	)

	// Отправляем отчет
	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:    adminID,
		Text:      messageText,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{
					{
						Text:         "🎟️ Управление промокодами",
						CallbackData: "admin_promocodes",
					},
				},
			},
		},
	})

	if err != nil {
		return fmt.Errorf("failed to send daily promo code report: %w", err)
	}

	slog.Info("Daily promo code report sent successfully")
	return nil
}
