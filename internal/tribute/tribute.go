package tribute

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"log/slog"
	"net/http"
	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/payment"
	"strings"
	"time"
)

type Client struct {
	paymentService     *payment.PaymentService
	customerRepository *database.CustomerRepository
}

func NewClient(paymentService *payment.PaymentService, customerRepository *database.CustomerRepository) *Client {
	return &Client{
		paymentService:     paymentService,
		customerRepository: customerRepository,
	}
}

func (c *Client) WebHookHandler() http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx, cancel := context.WithTimeout(r.Context(), time.Second*60)
		defer cancel()
		body, err := io.ReadAll(r.Body)
		if err != nil {
			slog.Error("webhook: read body error", "error", err)
			http.Error(w, "invalid body", http.StatusBadRequest)
			return
		}
		defer r.Body.Close()

		signature := r.Header.Get("trbt-signature")
		if signature == "" {
			http.Error(w, "missing signature", http.StatusUnauthorized)
			return
		}

		secret := config.GetTributeAPIKey()
		mac := hmac.New(sha256.New, []byte(secret))
		mac.Write(body)
		expected := hex.EncodeToString(mac.Sum(nil))

		if !hmac.Equal([]byte(expected), []byte(signature)) {
			log.Printf("webhook: bad signature (expected %s)", expected)
			http.Error(w, "invalid signature", http.StatusUnauthorized)
			return
		}

		var wh SubscriptionWebhook
		if err := json.Unmarshal(body, &wh); err != nil {
			slog.Error("webhook: unmarshal error", "error", err, "payload", string(body))
			http.Error(w, "invalid json", http.StatusBadRequest)
			return
		}

		// Обрабатываем различные типы событий
		switch WebhookEventType(wh.Name) {
		case WebhookEventNewSubscription, WebhookEventRenewSubscription:
			err := c.handleSubscriptionEvent(ctx, wh, body)
			if err != nil {
				slog.Error("webhook: subscription event error", "error", err, "event", wh.Name)
				http.Error(w, "internal server error", http.StatusInternalServerError)
				return
			}
		case WebhookEventCancelSubscription:
			err := c.handleCancelSubscriptionEvent(ctx, wh)
			if err != nil {
				slog.Error("webhook: cancel subscription error", "error", err, "event", wh.Name)
				http.Error(w, "internal server error", http.StatusInternalServerError)
				return
			}
		case WebhookEventFailedPayment:
			err := c.handleFailedPaymentEvent(ctx, wh)
			if err != nil {
				slog.Error("webhook: failed payment error", "error", err, "event", wh.Name)
				http.Error(w, "internal server error", http.StatusInternalServerError)
				return
			}
		default:
			slog.Info("webhook: unknown event type", "event", wh.Name)
		}

		w.WriteHeader(http.StatusOK)
	})
}

// handleSubscriptionEvent - обрабатывает события новой подписки и продления
func (c *Client) handleSubscriptionEvent(ctx context.Context, wh SubscriptionWebhook, body []byte) error {
	months := convertPeriodToMonths(wh.Payload.Period)

	customer, err := c.customerRepository.FindByTelegramId(ctx, wh.Payload.TelegramUserID)
	if err != nil {
		return fmt.Errorf("failed to find customer: %w", err)
	}

	_, purchaseId, err := c.paymentService.CreatePurchase(ctx, wh.Payload.Amount, months, customer, database.InvoiceTypeTribute)
	if err != nil {
		slog.Error("webhook: create purchase error", "error", err, "payload", string(body))
		return fmt.Errorf("failed to create purchase: %w", err)
	}

	err = c.paymentService.ProcessPurchaseById(ctx, purchaseId)
	if err != nil {
		slog.Error("webhook: process purchase error", "error", err, "payload", string(body))
		return fmt.Errorf("failed to process purchase: %w", err)
	}

	slog.Info("Tribute subscription processed successfully",
		"event", wh.Name,
		"customer_id", customer.ID,
		"telegram_id", wh.Payload.TelegramUserID,
		"purchase_id", purchaseId,
		"amount", wh.Payload.Amount,
		"period", wh.Payload.Period)

	return nil
}

// handleCancelSubscriptionEvent - обрабатывает отмену подписки
func (c *Client) handleCancelSubscriptionEvent(ctx context.Context, wh SubscriptionWebhook) error {
	customer, err := c.customerRepository.FindByTelegramId(ctx, wh.Payload.TelegramUserID)
	if err != nil {
		return fmt.Errorf("failed to find customer: %w", err)
	}

	slog.Info("Tribute subscription cancelled",
		"customer_id", customer.ID,
		"telegram_id", wh.Payload.TelegramUserID,
		"subscription_id", wh.Payload.SubscriptionID)

	// TODO: Здесь можно добавить логику отключения автопродления в нашей системе
	// или отправку уведомления пользователю об отмене подписки

	return nil
}

// handleFailedPaymentEvent - обрабатывает неудачные платежи
func (c *Client) handleFailedPaymentEvent(ctx context.Context, wh SubscriptionWebhook) error {
	customer, err := c.customerRepository.FindByTelegramId(ctx, wh.Payload.TelegramUserID)
	if err != nil {
		return fmt.Errorf("failed to find customer: %w", err)
	}

	slog.Warn("Tribute payment failed",
		"customer_id", customer.ID,
		"telegram_id", wh.Payload.TelegramUserID,
		"subscription_id", wh.Payload.SubscriptionID,
		"amount", wh.Payload.Amount)

	// TODO: Здесь можно добавить логику уведомления пользователя о неудачном платеже
	// или попытку альтернативного способа оплаты

	return nil
}

func convertPeriodToMonths(period string) int {
	switch strings.ToLower(period) {
	case "monthly":
		return 1
	case "quarterly", "3-month", "3months", "3-months", "q":
		return 3
	case "halfyearly":
		return 6
	case "yearly", "annual", "y":
		return 12
	default:
		return 1
	}
}
