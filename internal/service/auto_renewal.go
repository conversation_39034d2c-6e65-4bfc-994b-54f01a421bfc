package service

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/interfaces"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// AutoRenewalService - сервис для управления автопродлением подписок
type AutoRenewalService struct {
	autoRenewalRepository *database.AutoRenewalRepository
	customerRepository    *database.CustomerRepository
	tariffRepository      *database.TariffRepository
	telegramBot           *bot.Bot
	paymentInterface      interfaces.AutoRenewalPaymentInterface
}

// NewAutoRenewalService - создает новый экземпляр сервиса автопродления
func NewAutoRenewalService(
	autoRenewalRepository *database.AutoRenewalRepository,
	customerRepository *database.CustomerRepository,
	tariffRepository *database.TariffRepository,
	telegramBot *bot.Bot,
	paymentInterface interfaces.AutoRenewalPaymentInterface,
) *AutoRenewalService {
	return &AutoRenewalService{
		autoRenewalRepository: autoRenewalRepository,
		customerRepository:    customerRepository,
		tariffRepository:      tariffRepository,
		telegramBot:           telegramBot,
		paymentInterface:      paymentInterface,
	}
}

// EnableAutoRenewal - включает автопродление для пользователя
func (s *AutoRenewalService) EnableAutoRenewal(ctx context.Context, customerID int64, paymentMethod string, tariffCode string, savedPaymentData map[string]any) error {
	// Проверяем, что тариф существует
	tariff, err := s.tariffRepository.GetByCode(ctx, tariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}
	if tariff == nil {
		return fmt.Errorf("tariff with code %s not found", tariffCode)
	}

	// Проверяем, что способ оплаты поддерживается
	if !s.isPaymentMethodSupported(paymentMethod) {
		return fmt.Errorf("payment method %s is not supported for auto renewal", paymentMethod)
	}

	// Создаем или обновляем настройки автопродления
	settings := &database.AutoRenewalSettings{
		CustomerID:       customerID,
		Enabled:          true,
		PaymentMethod:    paymentMethod,
		SavedPaymentData: savedPaymentData,
		TariffCode:       tariffCode,
	}

	err = s.autoRenewalRepository.CreateOrUpdateSettings(ctx, settings)
	if err != nil {
		return fmt.Errorf("failed to create or update auto renewal settings: %w", err)
	}

	slog.Info("Auto renewal enabled", "customer_id", customerID, "payment_method", paymentMethod, "tariff_code", tariffCode)
	return nil
}

// DisableAutoRenewal - отключает автопродление для пользователя
func (s *AutoRenewalService) DisableAutoRenewal(ctx context.Context, customerID int64) error {
	err := s.autoRenewalRepository.DisableAutoRenewal(ctx, customerID)
	if err != nil {
		return fmt.Errorf("failed to disable auto renewal: %w", err)
	}

	slog.Info("Auto renewal disabled", "customer_id", customerID)
	return nil
}

// GetAutoRenewalSettings - получает настройки автопродления для пользователя
func (s *AutoRenewalService) GetAutoRenewalSettings(ctx context.Context, customerID int64) (*database.AutoRenewalSettings, error) {
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get auto renewal settings: %w", err)
	}
	return settings, nil
}

// ProcessAutoRenewals - обрабатывает автопродления для пользователей с истекающими подписками
func (s *AutoRenewalService) ProcessAutoRenewals(ctx context.Context) error {
	// Получаем настройки автопродления для пользователей с подписками, истекающими в течение часа
	settings, err := s.autoRenewalRepository.GetEnabledSettingsForRenewal(ctx, time.Now().Add(time.Hour))
	if err != nil {
		return fmt.Errorf("failed to get enabled settings for renewal: %w", err)
	}

	slog.Info("Processing auto renewals", "count", len(settings))

	for _, setting := range settings {
		err := s.processAutoRenewalForCustomer(ctx, setting)
		if err != nil {
			slog.Error("Failed to process auto renewal for customer",
				"customer_id", setting.CustomerID,
				"error", err)
			continue
		}
	}

	return nil
}

// processAutoRenewalForCustomer - обрабатывает автопродление для конкретного пользователя
func (s *AutoRenewalService) processAutoRenewalForCustomer(ctx context.Context, settings database.AutoRenewalSettings) error {
	// Получаем информацию о пользователе
	customer, err := s.customerRepository.FindById(ctx, settings.CustomerID)
	if err != nil {
		return fmt.Errorf("failed to get customer: %w", err)
	}
	if customer == nil {
		return fmt.Errorf("customer not found")
	}

	// Получаем информацию о тарифе
	tariff, err := s.tariffRepository.GetByCode(ctx, settings.TariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}
	if tariff == nil {
		return fmt.Errorf("tariff not found")
	}

	// Создаем запись в истории (статус pending)
	historyRecord := &database.AutoRenewalHistory{
		CustomerID:            settings.CustomerID,
		AutoRenewalSettingsID: settings.ID,
		Status:                database.AutoRenewalHistoryStatusPending,
		PaymentMethod:         &settings.PaymentMethod,
		TariffCode:            &settings.TariffCode,
	}

	err = s.autoRenewalRepository.CreateHistoryRecord(ctx, historyRecord)
	if err != nil {
		return fmt.Errorf("failed to create history record: %w", err)
	}

	// Проверяем поддерживаемость способа оплаты
	if !s.isPaymentMethodSupported(settings.PaymentMethod) {
		errorMsg := fmt.Sprintf("unsupported payment method: %s", settings.PaymentMethod)
		s.updateHistoryRecordWithError(ctx, historyRecord.ID, errorMsg)
		return fmt.Errorf("unsupported payment method: %s", settings.PaymentMethod)
	}

	// Пытаемся создать автоматический платеж
	err = s.createAutoRenewalPayment(ctx, customer, tariff, settings, historyRecord.ID)
	if err != nil {
		errorMsg := fmt.Sprintf("failed to create auto renewal payment: %v", err)
		s.updateHistoryRecordWithError(ctx, historyRecord.ID, errorMsg)

		// Отправляем уведомление о неудачном автопродлении
		s.sendAutoRenewalFailedNotification(ctx, customer, tariff, err.Error())

		return fmt.Errorf("failed to create auto renewal payment: %w", err)
	}

	slog.Info("Auto renewal processed successfully",
		"customer_id", settings.CustomerID,
		"tariff_code", settings.TariffCode,
		"payment_method", settings.PaymentMethod)

	return nil
}

// createAutoRenewalPayment - создает автоматический платеж для продления подписки
func (s *AutoRenewalService) createAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff, settings database.AutoRenewalSettings, historyID int64) error {
	// Используем интерфейс для создания автоматического платежа
	purchaseID, err := s.paymentInterface.CreateAutoRenewalPayment(ctx, customer, tariff, settings.PaymentMethod, settings.SavedPaymentData)
	if err != nil {
		return fmt.Errorf("failed to create auto renewal payment: %w", err)
	}

	// Обновляем запись в истории с успешным результатом
	s.updateHistoryRecordWithSuccess(ctx, historyID, purchaseID, float64(tariff.PriceRUB), "RUB")

	return nil
}

// isPaymentMethodSupported - проверяет, поддерживается ли способ оплаты для автопродления
func (s *AutoRenewalService) isPaymentMethodSupported(paymentMethod string) bool {
	supportedMethods := []string{"yookasa", "cryptopay", "telegram", "tribute"}
	for _, method := range supportedMethods {
		if method == paymentMethod {
			return true
		}
	}
	return false
}

// EnableAutoRenewalAfterPayment - включает автопродление после успешной оплаты
func (s *AutoRenewalService) EnableAutoRenewalAfterPayment(ctx context.Context, customerID int64, paymentMethod string, tariffCode string, paymentMethodData map[string]any) error {
	// Проверяем, поддерживается ли способ оплаты для автопродления
	if !s.isPaymentMethodSupported(paymentMethod) {
		slog.Warn("Payment method not supported for auto renewal",
			"payment_method", paymentMethod,
			"customer_id", customerID)
		return nil // Не возвращаем ошибку, просто не включаем автопродление
	}

	// Проверяем, существует ли тариф
	tariff, err := s.tariffRepository.GetByCode(ctx, tariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}
	if tariff == nil {
		return fmt.Errorf("tariff not found: %s", tariffCode)
	}

	// Включаем автопродление
	err = s.EnableAutoRenewal(ctx, customerID, paymentMethod, tariffCode, paymentMethodData)
	if err != nil {
		return fmt.Errorf("failed to enable auto renewal: %w", err)
	}

	slog.Info("Auto renewal enabled successfully after payment",
		"customer_id", customerID,
		"payment_method", paymentMethod,
		"tariff_code", tariffCode)

	return nil
}

// updateHistoryRecordWithError - обновляет запись в истории с ошибкой
func (s *AutoRenewalService) updateHistoryRecordWithError(ctx context.Context, historyID int64, errorMessage string) {
	err := s.autoRenewalRepository.UpdateHistoryRecordWithError(ctx, historyID, errorMessage)
	if err != nil {
		slog.Error("Failed to update history record with error", "history_id", historyID, "error", err)
	}
}

// updateHistoryRecordWithSuccess - обновляет запись в истории при успешном автопродлении
func (s *AutoRenewalService) updateHistoryRecordWithSuccess(ctx context.Context, historyID int64, purchaseID int64, amount float64, currency string) {
	err := s.autoRenewalRepository.UpdateHistoryRecordWithSuccess(ctx, historyID, purchaseID, amount, currency)
	if err != nil {
		slog.Error("Failed to update history record with success", "history_id", historyID, "error", err)
	}
}

// sendAutoRenewalFailedNotification - отправляет уведомление о неудачном автопродлении
func (s *AutoRenewalService) sendAutoRenewalFailedNotification(ctx context.Context, customer *database.Customer, tariff *database.Tariff, errorMessage string) {
	// Получаем язык пользователя
	langCode := customer.Language
	if langCode == "" {
		langCode = "ru" // Язык по умолчанию
	}

	// Формируем текст уведомления
	messageText := fmt.Sprintf(
		"❌ <b>Ошибка автопродления</b>\n\n"+
			"📦 Тариф: <b>%s</b>\n"+
			"⏰ Дата окончания: <b>%s</b>\n\n"+
			"🔴 Причина: %s\n\n"+
			"Для продления подписки воспользуйтесь ручным способом оплаты.",
		tariff.Title,
		customer.ExpireAt.Format("02.01.2006 15:04"),
		errorMessage,
	)

	// Создаем клавиатуру с кнопками действий
	keyboard := models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text:         "🛒 Продлить вручную",
					CallbackData: "buy",
				},
			},
			{
				{
					Text:         "⚙️ Настроить автопродление",
					CallbackData: "auto_renewal_toggle",
				},
			},
		},
	}

	// Отправляем уведомление
	_, err := s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Failed to send auto renewal failed notification",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID,
			"error", err)
	} else {
		slog.Info("Auto renewal failed notification sent successfully",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID)
	}
}

// GetAutoRenewalHistory - получает историю автопродлений для пользователя
func (s *AutoRenewalService) GetAutoRenewalHistory(ctx context.Context, customerID int64, limit int) ([]database.AutoRenewalHistory, error) {
	history, err := s.autoRenewalRepository.GetHistoryByCustomerID(ctx, customerID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get auto renewal history: %w", err)
	}
	return history, nil
}

// DeleteAutoRenewalSettings - удаляет настройки автопродления и все сохраненные платежные данные
func (s *AutoRenewalService) DeleteAutoRenewalSettings(ctx context.Context, customerID int64) error {
	err := s.autoRenewalRepository.DeleteSettings(ctx, customerID)
	if err != nil {
		return fmt.Errorf("failed to delete auto renewal settings: %w", err)
	}

	slog.Info("Auto renewal settings deleted", "customer_id", customerID)
	return nil
}
