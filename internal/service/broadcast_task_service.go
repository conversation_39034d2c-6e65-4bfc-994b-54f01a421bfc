package service

import (
	"context"
	"remnawave-tg-shop-bot/internal/database"
)

// BroadcastTaskService предоставляет методы для работы с задачами отложенной рассылки.
type BroadcastTaskService struct {
	repo *database.BroadcastTaskRepository
}

// NewBroadcastTaskService создает новый сервис для работы с задачами рассылки.
func NewBroadcastTaskService(repo *database.BroadcastTaskRepository) *BroadcastTaskService {
	return &BroadcastTaskService{repo: repo}
}

// Create создает новую задачу рассылки.
func (s *BroadcastTaskService) Create(ctx context.Context, task *database.BroadcastTask) (int64, error) {
	return s.repo.Create(ctx, task)
}

// GetByID возвращает задачу по идентификатору.
func (s *BroadcastTaskService) GetByID(ctx context.Context, id int64) (*database.BroadcastTask, error) {
	return s.repo.GetByID(ctx, id)
}

// GetAll возвращает все задачи рассылки.
func (s *BroadcastTaskService) GetAll(ctx context.Context) ([]database.BroadcastTask, error) {
	return s.repo.GetAll(ctx)
}

// UpdateStatus обновляет статус задачи.
func (s *BroadcastTaskService) UpdateStatus(ctx context.Context, id int64, status database.BroadcastTaskStatus) error {
	return s.repo.UpdateStatus(ctx, id, status)
}

// Delete удаляет задачу по идентификатору.
func (s *BroadcastTaskService) Delete(ctx context.Context, id int64) error {
	return s.repo.Delete(ctx, id)
}
 