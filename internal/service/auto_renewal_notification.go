package service

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/translation"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// AutoRenewalNotificationService - сервис для отправки уведомлений об автопродлении
type AutoRenewalNotificationService struct {
	autoRenewalRepository *database.AutoRenewalRepository
	tariffRepository      *database.TariffRepository
	telegramBot           *bot.Bot
	translation           *translation.Manager
}

// NewAutoRenewalNotificationService - создает новый экземпляр сервиса уведомлений об автопродлении
func NewAutoRenewalNotificationService(
	autoRenewalRepository *database.AutoRenewalRepository,
	tariffRepository *database.TariffRepository,
	telegramBot *bot.Bot,
	translation *translation.Manager,
) *AutoRenewalNotificationService {
	return &AutoRenewalNotificationService{
		autoRenewalRepository: autoRenewalRepository,
		tariffRepository:      tariffRepository,
		telegramBot:           telegramBot,
		translation:           translation,
	}
}

// SendAutoRenewalNotifications24h - отправляет уведомления за 24 часа до автопродления
func (s *AutoRenewalNotificationService) SendAutoRenewalNotifications24h(ctx context.Context) error {
	customers, err := s.autoRenewalRepository.GetCustomersForNotification(ctx, database.AutoRenewalNotification24h, 24*time.Hour)
	if err != nil {
		return fmt.Errorf("failed to get customers for 24h notification: %w", err)
	}

	slog.Info("Sending 24h auto renewal notifications", "count", len(customers))

	for _, customer := range customers {
		err := s.send24hNotification(ctx, customer)
		if err != nil {
			slog.Error("Failed to send 24h auto renewal notification",
				"customer_id", customer.ID,
				"telegram_id", customer.TelegramID,
				"error", err)
			continue
		}

		// Записываем факт отправки уведомления
		notification := &database.AutoRenewalNotification{
			CustomerID:            customer.ID,
			NotificationType:      database.AutoRenewalNotification24h,
			SubscriptionExpiresAt: *customer.ExpireAt,
		}

		err = s.autoRenewalRepository.CreateNotification(ctx, notification)
		if err != nil {
			slog.Error("Failed to create notification record",
				"customer_id", customer.ID,
				"error", err)
		}

		slog.Info("24h auto renewal notification sent successfully",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID)
	}

	return nil
}

// SendAutoRenewalNotifications12h - отправляет уведомления за 12 часов до автопродления
func (s *AutoRenewalNotificationService) SendAutoRenewalNotifications12h(ctx context.Context) error {
	customers, err := s.autoRenewalRepository.GetCustomersForNotification(ctx, database.AutoRenewalNotification12h, 12*time.Hour)
	if err != nil {
		return fmt.Errorf("failed to get customers for 12h notification: %w", err)
	}

	slog.Info("Sending 12h auto renewal notifications", "count", len(customers))

	for _, customer := range customers {
		err := s.send12hNotification(ctx, customer)
		if err != nil {
			slog.Error("Failed to send 12h auto renewal notification",
				"customer_id", customer.ID,
				"telegram_id", customer.TelegramID,
				"error", err)
			continue
		}

		// Записываем факт отправки уведомления
		notification := &database.AutoRenewalNotification{
			CustomerID:            customer.ID,
			NotificationType:      database.AutoRenewalNotification12h,
			SubscriptionExpiresAt: *customer.ExpireAt,
		}

		err = s.autoRenewalRepository.CreateNotification(ctx, notification)
		if err != nil {
			slog.Error("Failed to create notification record",
				"customer_id", customer.ID,
				"error", err)
		}

		slog.Info("12h auto renewal notification sent successfully",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID)
	}

	return nil
}

// send24hNotification - отправляет уведомление за 24 часа до автопродления
func (s *AutoRenewalNotificationService) send24hNotification(ctx context.Context, customer database.Customer) error {
	// Получаем настройки автопродления для пользователя
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customer.ID)
	if err != nil {
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	// Получаем информацию о тарифе
	tariff, err := s.tariffRepository.GetByCode(ctx, settings.TariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}

	// Определяем стоимость в зависимости от способа оплаты
	var price int
	var currency string
	switch settings.PaymentMethod {
	case "yookasa", "cryptopay", "tribute":
		price = tariff.PriceRUB
		currency = "₽"
	case "telegram":
		price = tariff.PriceStars
		currency = "⭐"
	default:
		price = tariff.PriceRUB
		currency = "₽"
	}

	expireDate := customer.ExpireAt.Format("02.01.2006 15:04")

	// Формируем текст уведомления
	messageText := fmt.Sprintf(
		s.translation.GetText(customer.Language, "auto_renewal_24h_notification"),
		tariff.Title,
		expireDate,
		price,
		currency,
		s.getPaymentMethodName(settings.PaymentMethod, customer.Language),
	)

	// Создаем клавиатуру с действиями
	keyboard := s.create24hNotificationKeyboard(customer.Language)

	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	return err
}

// send12hNotification - отправляет уведомление за 12 часов до автопродления
func (s *AutoRenewalNotificationService) send12hNotification(ctx context.Context, customer database.Customer) error {
	// Получаем настройки автопродления для пользователя
	settings, err := s.autoRenewalRepository.GetSettingsByCustomerID(ctx, customer.ID)
	if err != nil {
		return fmt.Errorf("failed to get auto renewal settings: %w", err)
	}

	// Получаем информацию о тарифе
	tariff, err := s.tariffRepository.GetByCode(ctx, settings.TariffCode)
	if err != nil {
		return fmt.Errorf("failed to get tariff: %w", err)
	}

	// Определяем стоимость в зависимости от способа оплаты
	var price int
	var currency string
	switch settings.PaymentMethod {
	case "yookasa", "cryptopay", "tribute":
		price = tariff.PriceRUB
		currency = "₽"
	case "telegram":
		price = tariff.PriceStars
		currency = "⭐"
	default:
		price = tariff.PriceRUB
		currency = "₽"
	}

	expireDate := customer.ExpireAt.Format("02.01.2006 15:04")

	// Формируем текст уведомления
	messageText := fmt.Sprintf(
		s.translation.GetText(customer.Language, "auto_renewal_12h_notification"),
		tariff.Title,
		expireDate,
		price,
		currency,
		s.getPaymentMethodName(settings.PaymentMethod, customer.Language),
	)

	// Создаем клавиатуру с действиями
	keyboard := s.create12hNotificationKeyboard(customer.Language)

	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	return err
}

// create24hNotificationKeyboard - создает клавиатуру для уведомления за 24 часа
func (s *AutoRenewalNotificationService) create24hNotificationKeyboard(langCode string) models.InlineKeyboardMarkup {
	return models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text:         s.translation.GetText(langCode, "manual_renewal_button"),
					CallbackData: "buy",
				},
			},
			{
				{
					Text:         s.translation.GetText(langCode, "change_payment_method_button"),
					CallbackData: "auto_renewal_change_payment",
				},
			},
			{
				{
					Text:         s.translation.GetText(langCode, "disable_auto_renewal_button"),
					CallbackData: "auto_renewal_disable",
				},
			},
		},
	}
}

// create12hNotificationKeyboard - создает клавиатуру для уведомления за 12 часов
func (s *AutoRenewalNotificationService) create12hNotificationKeyboard(langCode string) models.InlineKeyboardMarkup {
	return models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text:         s.translation.GetText(langCode, "manual_renewal_button"),
					CallbackData: "buy",
				},
			},
			{
				{
					Text:         s.translation.GetText(langCode, "disable_auto_renewal_button"),
					CallbackData: "auto_renewal_disable",
				},
			},
		},
	}
}

// getPaymentMethodName - возвращает локализованное название способа оплаты
func (s *AutoRenewalNotificationService) getPaymentMethodName(paymentMethod, langCode string) string {
	switch paymentMethod {
	case "yookasa":
		return s.translation.GetText(langCode, "payment_method_card")
	case "cryptopay":
		return s.translation.GetText(langCode, "payment_method_crypto")
	case "telegram":
		return s.translation.GetText(langCode, "payment_method_stars")
	case "tribute":
		return s.translation.GetText(langCode, "payment_method_tribute")
	default:
		return paymentMethod
	}
}
