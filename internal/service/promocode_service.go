package service

import (
	"context"
	"errors"
	"fmt"
	"math"
	"remnawave-tg-shop-bot/internal/database"
	"time"
)

// PromoCodeValidationError - ошибка валидации промокода
type PromoCodeValidationError struct {
	Code    string
	Message string
}

func (e *PromoCodeValidationError) Error() string {
	return e.Message
}

// Константы ошибок валидации
var (
	ErrPromoCodeNotFound      = &PromoCodeValidationError{Code: "not_found", Message: "Промокод не существует"}
	ErrPromoCodeExpired       = &PromoCodeValidationError{Code: "expired", Message: "Срок действия промокода истек"}
	ErrPromoCodeInactive      = &PromoCodeValidationError{Code: "inactive", Message: "Промокод неактивен"}
	ErrPromoCodeLimitReached  = &PromoCodeValidationError{Code: "limit_reached", Message: "Достигнут лимит использований промокода"}
	ErrPromoCodeAlreadyUsed   = &PromoCodeValidationError{Code: "already_used", Message: "Промокод уже был использован"}
	ErrPromoCodeUserType      = &PromoCodeValidationError{Code: "user_type", Message: "Промокод недоступен для вашего типа пользователя"}
	ErrPromoCodeTariffType    = &PromoCodeValidationError{Code: "tariff_type", Message: "Промокод не применим к выбранному тарифу"}
)

// PromoCodeService - сервис для работы с промокодами
type PromoCodeService struct {
	promoCodeRepo      *database.PromoCodeRepository
	promoCodeUsageRepo *database.PromoCodeUsageRepository
}

// NewPromoCodeService создает новый сервис промокодов
func NewPromoCodeService(promoCodeRepo *database.PromoCodeRepository, promoCodeUsageRepo *database.PromoCodeUsageRepository) *PromoCodeService {
	return &PromoCodeService{
		promoCodeRepo:      promoCodeRepo,
		promoCodeUsageRepo: promoCodeUsageRepo,
	}
}

// ValidatePromoCode валидирует промокод для использования
func (s *PromoCodeService) ValidatePromoCode(ctx context.Context, code string, customer *database.Customer, tariff *database.Tariff) (*database.PromoCode, error) {
	// Получаем промокод по коду
	promoCode, err := s.promoCodeRepo.GetByCode(ctx, code)
	if err != nil {
		return nil, err
	}
	
	if promoCode == nil {
		return nil, ErrPromoCodeNotFound
	}
	
	// Проверяем активность
	if !promoCode.Active {
		return nil, ErrPromoCodeInactive
	}
	
	// Проверяем срок действия
	if promoCode.IsExpired() {
		return nil, ErrPromoCodeExpired
	}
	
	// Проверяем лимит использований
	if promoCode.IsLimitReached() {
		return nil, ErrPromoCodeLimitReached
	}
	
	// Проверяем, не использовал ли пользователь уже этот промокод
	hasUsed, err := s.promoCodeUsageRepo.HasUsedPromoCode(ctx, promoCode.ID, customer.ID)
	if err != nil {
		return nil, err
	}
	if hasUsed {
		return nil, ErrPromoCodeAlreadyUsed
	}
	
	// Проверяем тип пользователя
	if !promoCode.CanBeUsedBy(customer) {
		return nil, ErrPromoCodeUserType
	}
	
	// Проверяем применимость к тарифу
	if !promoCode.ApplicableTariffs.IsApplicableToTariff(tariff.Code) {
		return nil, ErrPromoCodeTariffType
	}
	
	return promoCode, nil
}

// CalculateDiscountedPrice рассчитывает цену со скидкой
func (s *PromoCodeService) CalculateDiscountedPrice(originalPrice int, discountPercent int, currency string) (discountAmount int, finalPrice int) {
	// Рассчитываем размер скидки
	discountAmount = int(math.Round(float64(originalPrice) * float64(discountPercent) / 100.0))
	
	// Рассчитываем итоговую цену
	finalPrice = originalPrice - discountAmount
	
	// Применяем правила округления
	switch currency {
	case "RUB":
		// Рубли: округление в большую сторону до целого числа
		if finalPrice < 1 {
			finalPrice = 1
		}
	case "STARS":
		// Telegram Stars: округление в большую сторону до целого числа
		if finalPrice < 1 {
			finalPrice = 1
		}
	}
	
	// Пересчитываем размер скидки с учетом округления
	discountAmount = originalPrice - finalPrice
	
	return discountAmount, finalPrice
}

// ApplyPromoCode применяет промокод к покупке
func (s *PromoCodeService) ApplyPromoCode(ctx context.Context, promoCode *database.PromoCode, customer *database.Customer, tariff *database.Tariff, purchaseID int64, currency string) (*database.PromoCodeUsage, error) {
	// Определяем оригинальную цену в зависимости от валюты
	var originalPrice int
	switch currency {
	case "RUB":
		originalPrice = tariff.PriceRUB
	case "STARS":
		originalPrice = tariff.PriceStars
	default:
		return nil, fmt.Errorf("неподдерживаемая валюта: %s", currency)
	}
	
	// Рассчитываем цену со скидкой
	discountAmount, finalPrice := s.CalculateDiscountedPrice(originalPrice, promoCode.DiscountPercent, currency)
	
	// Создаем запись об использовании промокода
	usage := &database.PromoCodeUsage{
		PromoCodeID:    promoCode.ID,
		CustomerID:     customer.ID,
		PurchaseID:     purchaseID,
		TariffCode:     tariff.Code,
		OriginalPrice:  originalPrice,
		DiscountAmount: discountAmount,
		FinalPrice:     finalPrice,
		Currency:       currency,
	}
	
	// Сохраняем использование промокода
	usageID, err := s.promoCodeUsageRepo.Create(ctx, usage)
	if err != nil {
		return nil, err
	}
	
	usage.ID = usageID
	return usage, nil
}

// GetPromoCodeStatistics получает статистику использования промокода
func (s *PromoCodeService) GetPromoCodeStatistics(ctx context.Context, promoCodeID int64) (map[string]interface{}, error) {
	return s.promoCodeUsageRepo.GetStatistics(ctx, promoCodeID)
}

// GetExpiringPromoCodes получает промокоды, которые истекают в течение указанного времени
func (s *PromoCodeService) GetExpiringPromoCodes(ctx context.Context, within time.Duration) ([]database.PromoCode, error) {
	return s.promoCodeRepo.GetExpiringPromoCodes(ctx, within)
}

// CreatePromoCode создает новый промокод
func (s *PromoCodeService) CreatePromoCode(ctx context.Context, pc *database.PromoCode) (int64, error) {
	// Валидация данных
	if err := s.validatePromoCodeData(pc); err != nil {
		return 0, err
	}
	
	return s.promoCodeRepo.Create(ctx, pc)
}

// UpdatePromoCode обновляет промокод
func (s *PromoCodeService) UpdatePromoCode(ctx context.Context, pc *database.PromoCode) error {
	// Валидация данных
	if err := s.validatePromoCodeData(pc); err != nil {
		return err
	}
	
	return s.promoCodeRepo.Update(ctx, pc)
}

// validatePromoCodeData валидирует данные промокода
func (s *PromoCodeService) validatePromoCodeData(pc *database.PromoCode) error {
	// Проверяем код промокода
	if pc.Code == "" {
		return errors.New("код промокода не может быть пустым")
	}
	
	if len(pc.Code) > 50 {
		return errors.New("код промокода не может быть длиннее 50 символов")
	}
	
	// Проверяем описание
	if pc.Description == "" {
		return errors.New("описание промокода не может быть пустым")
	}
	
	// Проверяем процент скидки
	if pc.DiscountPercent < 1 || pc.DiscountPercent > 100 {
		return errors.New("процент скидки должен быть от 1 до 100")
	}
	
	// Проверяем тип срока действия
	switch pc.ExpiryType {
	case database.PromoCodeExpiryTypeHours, database.PromoCodeExpiryTypeDays:
		if pc.ExpiryValue == nil || *pc.ExpiryValue <= 0 {
			return fmt.Errorf("для типа срока действия %s необходимо указать положительное значение", pc.ExpiryType)
		}
	case database.PromoCodeExpiryTypeEndOfDay:
		// Для end_of_day значение не требуется
	default:
		return fmt.Errorf("неизвестный тип срока действия: %s", pc.ExpiryType)
	}
	
	// Проверяем лимит активаций
	if pc.ActivationLimit < 0 {
		return errors.New("лимит активаций не может быть отрицательным")
	}
	
	// Проверяем тип пользователей
	switch pc.UserType {
	case database.PromoCodeUserTypeWithSubscription, database.PromoCodeUserTypeWithoutSubscription, database.PromoCodeUserTypeAll:
		// OK
	default:
		return fmt.Errorf("неизвестный тип пользователей: %s", pc.UserType)
	}
	
	return nil
}

// DeletePromoCode удаляет промокод
func (s *PromoCodeService) DeletePromoCode(ctx context.Context, id int64) error {
	return s.promoCodeRepo.Delete(ctx, id)
}

// SetPromoCodeActive изменяет статус активности промокода
func (s *PromoCodeService) SetPromoCodeActive(ctx context.Context, id int64, active bool) error {
	return s.promoCodeRepo.SetActive(ctx, id, active)
}

// GetAllPromoCodes получает все промокоды
func (s *PromoCodeService) GetAllPromoCodes(ctx context.Context, onlyActive bool) ([]database.PromoCode, error) {
	return s.promoCodeRepo.GetAll(ctx, onlyActive)
}

// GetPromoCodeByCode получает промокод по коду
func (s *PromoCodeService) GetPromoCodeByCode(ctx context.Context, code string) (*database.PromoCode, error) {
	return s.promoCodeRepo.GetByCode(ctx, code)
}
