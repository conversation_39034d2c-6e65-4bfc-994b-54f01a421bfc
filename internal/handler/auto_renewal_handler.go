package handler

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/service"
	"remnawave-tg-shop-bot/internal/translation"
	"strings"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// AutoRenewalHandler - обработчик для управления автопродлением
type AutoRenewalHandler struct {
	autoRenewalService *service.AutoRenewalService
	customerRepository *database.CustomerRepository
	tariffRepository   *database.TariffRepository
	translation        *translation.Manager
}

// NewAutoRenewalHandler - создает новый экземпляр обработчика автопродления
func NewAutoRenewalHandler(
	autoRenewalService *service.AutoRenewalService,
	customerRepository *database.CustomerRepository,
	tariffRepository *database.TariffRepository,
	translation *translation.Manager,
) *AutoRenewalHandler {
	return &AutoRenewalHandler{
		autoRenewalService: autoRenewalService,
		customerRepository: customerRepository,
		tariffRepository:   tariffRepository,
		translation:        translation,
	}
}

// HandleAutoRenewalToggle - обработчик включения/выключения автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalToggle(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Получаем текущие настройки автопродления
	settings, err := h.autoRenewalService.GetAutoRenewalSettings(ctx, customer.ID)
	if err != nil && err.Error() != "no rows in result set" {
		slog.Error("Error getting auto renewal settings", "error", err)
		return
	}

	var messageText string
	var keyboard models.InlineKeyboardMarkup

	if settings == nil || !settings.Enabled {
		// Автопродление выключено, показываем опции для включения
		messageText = h.translation.GetText(langCode, "auto_renewal_setup_message")
		keyboard = h.createAutoRenewalSetupKeyboard(langCode)
	} else {
		// Автопродление включено, показываем опции для управления
		tariff, _ := h.tariffRepository.GetByCode(ctx, settings.TariffCode)
		messageText = h.buildAutoRenewalStatusText(settings, tariff, langCode)
		keyboard = h.createAutoRenewalManageKeyboard(langCode)
	}

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:      callback.Chat.ID,
		MessageID:   callback.ID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// HandleAutoRenewalDisable - обработчик отключения автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalDisable(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Отключаем автопродление
	err = h.autoRenewalService.DisableAutoRenewal(ctx, customer.ID)
	if err != nil {
		slog.Error("Error disabling auto renewal", "error", err)

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			Text:      h.translation.GetText(langCode, "auto_renewal_disable_error"),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackConnect}},
				},
			},
		})
		return
	}

	// Показываем сообщение об успешном отключении
	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		Text:      h.translation.GetText(langCode, "auto_renewal_disabled_success"),
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{{Text: h.translation.GetText(langCode, "back_to_subscription_button"), CallbackData: CallbackConnect}},
			},
		},
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// HandleAutoRenewalChangePayment - обработчик изменения способа оплаты для автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalChangePayment(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Показываем доступные способы оплаты
	messageText := h.translation.GetText(langCode, "auto_renewal_change_payment_message")
	keyboard := h.createPaymentMethodSelectionKeyboard(langCode)

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:      callback.Chat.ID,
		MessageID:   callback.ID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// HandleAutoRenewalDeleteData - обработчик удаления сохраненных платежных данных
func (h *AutoRenewalHandler) HandleAutoRenewalDeleteData(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Удаляем настройки автопродления и все сохраненные данные
	err = h.autoRenewalService.DeleteAutoRenewalSettings(ctx, customer.ID)
	if err != nil {
		slog.Error("Error deleting auto renewal settings", "error", err)

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			Text:      h.translation.GetText(langCode, "auto_renewal_delete_data_error"),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackConnect}},
				},
			},
		})
		return
	}

	// Показываем сообщение об успешном удалении
	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		Text:      h.translation.GetText(langCode, "auto_renewal_data_deleted_success"),
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{{Text: h.translation.GetText(langCode, "back_to_subscription_button"), CallbackData: CallbackConnect}},
			},
		},
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// createAutoRenewalSetupKeyboard - создает клавиатуру для настройки автопродления
func (h *AutoRenewalHandler) createAutoRenewalSetupKeyboard(langCode string) models.InlineKeyboardMarkup {
	return models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text:         h.translation.GetText(langCode, "setup_auto_renewal_button"),
					CallbackData: "auto_renewal_setup_start",
				},
			},
			{
				{
					Text:         h.translation.GetText(langCode, "back_button"),
					CallbackData: CallbackConnect,
				},
			},
		},
	}
}

// createAutoRenewalManageKeyboard - создает клавиатуру для управления автопродлением
func (h *AutoRenewalHandler) createAutoRenewalManageKeyboard(langCode string) models.InlineKeyboardMarkup {
	return models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text:         h.translation.GetText(langCode, "change_payment_method_button"),
					CallbackData: "auto_renewal_change_payment",
				},
			},
			{
				{
					Text:         h.translation.GetText(langCode, "disable_auto_renewal_button"),
					CallbackData: "auto_renewal_disable",
				},
			},
			{
				{
					Text:         h.translation.GetText(langCode, "delete_payment_data_button"),
					CallbackData: "auto_renewal_delete_data",
				},
			},
			{
				{
					Text:         h.translation.GetText(langCode, "back_button"),
					CallbackData: CallbackConnect,
				},
			},
		},
	}
}

// createPaymentMethodSelectionKeyboard - создает клавиатуру для выбора способа оплаты
func (h *AutoRenewalHandler) createPaymentMethodSelectionKeyboard(langCode string) models.InlineKeyboardMarkup {
	var keyboard [][]models.InlineKeyboardButton

	// Добавляем доступные способы оплаты
	// TODO: Проверить доступность каждого способа оплаты через конфигурацию
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "payment_method_card"),
			CallbackData: "auto_renewal_set_payment_yookasa",
		},
	})

	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "payment_method_crypto"),
			CallbackData: "auto_renewal_set_payment_cryptopay",
		},
	})

	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "payment_method_stars"),
			CallbackData: "auto_renewal_set_payment_telegram",
		},
	})

	// Кнопка назад
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "back_button"),
			CallbackData: "auto_renewal_toggle",
		},
	})

	return models.InlineKeyboardMarkup{InlineKeyboard: keyboard}
}

// buildAutoRenewalStatusText - формирует текст с информацией о статусе автопродления
func (h *AutoRenewalHandler) buildAutoRenewalStatusText(settings *database.AutoRenewalSettings, tariff *database.Tariff, langCode string) string {
	var info strings.Builder

	info.WriteString(h.translation.GetText(langCode, "auto_renewal_enabled_message"))
	info.WriteString("\n\n")

	if tariff != nil {
		info.WriteString(fmt.Sprintf(
			h.translation.GetText(langCode, "auto_renewal_tariff_info"),
			tariff.Title,
		))
		info.WriteString("\n")

		// Определяем стоимость в зависимости от способа оплаты
		var price int
		var currency string
		switch settings.PaymentMethod {
		case "yookasa", "cryptopay", "tribute":
			price = tariff.PriceRUB
			currency = "₽"
		case "telegram":
			price = tariff.PriceStars
			currency = "⭐"
		default:
			price = tariff.PriceRUB
			currency = "₽"
		}

		info.WriteString(fmt.Sprintf(
			h.translation.GetText(langCode, "auto_renewal_price_info"),
			price,
			currency,
		))
		info.WriteString("\n")
	}

	info.WriteString(fmt.Sprintf(
		h.translation.GetText(langCode, "auto_renewal_payment_method_info"),
		h.getPaymentMethodName(settings.PaymentMethod, langCode),
	))

	return info.String()
}

// getPaymentMethodName - возвращает локализованное название способа оплаты
func (h *AutoRenewalHandler) getPaymentMethodName(paymentMethod, langCode string) string {
	switch paymentMethod {
	case "yookasa":
		return h.translation.GetText(langCode, "payment_method_card")
	case "cryptopay":
		return h.translation.GetText(langCode, "payment_method_crypto")
	case "telegram":
		return h.translation.GetText(langCode, "payment_method_stars")
	case "tribute":
		return h.translation.GetText(langCode, "payment_method_tribute")
	default:
		return paymentMethod
	}
}

// HandleAutoRenewalSetupStart - начинает процесс настройки автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalSetupStart(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Показываем выбор тарифов для автопродления
	messageText := h.translation.GetText(langCode, "auto_renewal_select_tariff_message")
	keyboard := h.createTariffSelectionKeyboard(ctx, langCode)

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:      callback.Chat.ID,
		MessageID:   callback.ID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// HandleAutoRenewalSelectTariff - обрабатывает выбор тарифа для автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalSelectTariff(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	// Извлекаем код тарифа из callback data
	callbackData := update.CallbackQuery.Data
	tariffCode := callbackData[len("auto_renewal_select_tariff_"):] // Убираем префикс

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Проверяем, что тариф существует
	tariff, err := h.tariffRepository.GetByCode(ctx, tariffCode)
	if err != nil || tariff == nil {
		slog.Error("Tariff not found", "tariff_code", tariffCode, "error", err)
		return
	}

	// Показываем выбор способов оплаты
	messageText := fmt.Sprintf(
		h.translation.GetText(langCode, "auto_renewal_select_payment_message"),
		tariff.Title,
	)
	keyboard := h.createPaymentMethodSelectionKeyboardForSetup(langCode, tariffCode)

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:      callback.Chat.ID,
		MessageID:   callback.ID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// HandleAutoRenewalSelectPaymentMethod - обрабатывает выбор способа оплаты для автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalSelectPaymentMethod(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	// Извлекаем данные из callback data: auto_renewal_setup_payment_{method}_{tariffCode}
	callbackData := update.CallbackQuery.Data
	parts := strings.Split(callbackData, "_")
	if len(parts) < 5 {
		slog.Error("Invalid callback data format", "data", callbackData)
		return
	}

	paymentMethod := parts[3]
	tariffCode := parts[4]

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Получаем информацию о тарифе
	tariff, err := h.tariffRepository.GetByCode(ctx, tariffCode)
	if err != nil || tariff == nil {
		slog.Error("Tariff not found", "tariff_code", tariffCode, "error", err)
		return
	}

	// Показываем подтверждение настройки автопродления
	messageText := h.buildAutoRenewalConfirmationText(tariff, paymentMethod, langCode)
	keyboard := h.createAutoRenewalConfirmationKeyboard(langCode, paymentMethod, tariffCode)

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:      callback.Chat.ID,
		MessageID:   callback.ID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// HandleAutoRenewalConfirm - подтверждает настройку автопродления
func (h *AutoRenewalHandler) HandleAutoRenewalConfirm(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	// Извлекаем данные из callback data: auto_renewal_confirm_{method}_{tariffCode}
	callbackData := update.CallbackQuery.Data
	parts := strings.Split(callbackData, "_")
	if len(parts) < 4 {
		slog.Error("Invalid callback data format", "data", callbackData)
		return
	}

	paymentMethod := parts[3]
	tariffCode := parts[4]

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return
	}
	if customer == nil {
		slog.Error("Customer not found", "chat_id", callback.Chat.ID)
		return
	}

	// Создаем пустые сохраненные данные (будут заполнены при первом платеже)
	savedPaymentData := make(map[string]any)

	// Включаем автопродление
	err = h.autoRenewalService.EnableAutoRenewal(ctx, customer.ID, paymentMethod, tariffCode, savedPaymentData)
	if err != nil {
		slog.Error("Error enabling auto renewal", "error", err)

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			Text:      h.translation.GetText(langCode, "auto_renewal_setup_error"),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackConnect}},
				},
			},
		})
		return
	}

	// Показываем сообщение об успешной настройке
	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		Text:      h.translation.GetText(langCode, "auto_renewal_setup_success"),
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{{Text: h.translation.GetText(langCode, "back_to_subscription_button"), CallbackData: CallbackConnect}},
			},
		},
	})

	if err != nil {
		slog.Error("Error editing message", "error", err)
	}
}

// createTariffSelectionKeyboard - создает клавиатуру для выбора тарифа
func (h *AutoRenewalHandler) createTariffSelectionKeyboard(ctx context.Context, langCode string) models.InlineKeyboardMarkup {
	// Получаем все активные тарифы
	tariffs, err := h.tariffRepository.GetAll(ctx, true)
	if err != nil {
		slog.Error("Error getting tariffs", "error", err)
		return models.InlineKeyboardMarkup{}
	}

	var keyboard [][]models.InlineKeyboardButton

	// Добавляем кнопки для каждого тарифа
	for _, tariff := range tariffs {
		keyboard = append(keyboard, []models.InlineKeyboardButton{
			{
				Text:         fmt.Sprintf("%s - %d₽", tariff.Title, tariff.PriceRUB),
				CallbackData: fmt.Sprintf("auto_renewal_select_tariff_%s", tariff.Code),
			},
		})
	}

	// Кнопка назад
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "back_button"),
			CallbackData: "auto_renewal_toggle",
		},
	})

	return models.InlineKeyboardMarkup{InlineKeyboard: keyboard}
}

// createPaymentMethodSelectionKeyboardForSetup - создает клавиатуру для выбора способа оплаты при настройке
func (h *AutoRenewalHandler) createPaymentMethodSelectionKeyboardForSetup(langCode, tariffCode string) models.InlineKeyboardMarkup {
	var keyboard [][]models.InlineKeyboardButton

	// Добавляем доступные способы оплаты
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "payment_method_card"),
			CallbackData: fmt.Sprintf("auto_renewal_setup_payment_yookasa_%s", tariffCode),
		},
	})

	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "payment_method_crypto"),
			CallbackData: fmt.Sprintf("auto_renewal_setup_payment_cryptopay_%s", tariffCode),
		},
	})

	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "payment_method_stars"),
			CallbackData: fmt.Sprintf("auto_renewal_setup_payment_telegram_%s", tariffCode),
		},
	})

	// Кнопка назад
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         h.translation.GetText(langCode, "back_button"),
			CallbackData: "auto_renewal_setup_start",
		},
	})

	return models.InlineKeyboardMarkup{InlineKeyboard: keyboard}
}

// buildAutoRenewalConfirmationText - формирует текст подтверждения настройки автопродления
func (h *AutoRenewalHandler) buildAutoRenewalConfirmationText(tariff *database.Tariff, paymentMethod, langCode string) string {
	var price int
	var currency string
	switch paymentMethod {
	case "yookasa", "cryptopay", "tribute":
		price = tariff.PriceRUB
		currency = "₽"
	case "telegram":
		price = tariff.PriceStars
		currency = "⭐"
	default:
		price = tariff.PriceRUB
		currency = "₽"
	}

	paymentMethodName := h.getPaymentMethodName(paymentMethod, langCode)

	return fmt.Sprintf(
		h.translation.GetText(langCode, "auto_renewal_confirmation_message"),
		tariff.Title,
		price,
		currency,
		paymentMethodName,
	)
}

// createAutoRenewalConfirmationKeyboard - создает клавиатуру подтверждения настройки автопродления
func (h *AutoRenewalHandler) createAutoRenewalConfirmationKeyboard(langCode, paymentMethod, tariffCode string) models.InlineKeyboardMarkup {
	return models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text:         h.translation.GetText(langCode, "confirm_button"),
					CallbackData: fmt.Sprintf("auto_renewal_confirm_%s_%s", paymentMethod, tariffCode),
				},
			},
			{
				{
					Text:         h.translation.GetText(langCode, "back_button"),
					CallbackData: fmt.Sprintf("auto_renewal_select_tariff_%s", tariffCode),
				},
			},
		},
	}
}
