package handler

import (
	"context"
	"log/slog"
	"remnawave-tg-shop-bot/internal/cache"
	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/cryptopay"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/payment"
	"remnawave-tg-shop-bot/internal/service"
	"remnawave-tg-shop-bot/internal/sync"
	"remnawave-tg-shop-bot/internal/translation"
	"remnawave-tg-shop-bot/internal/yookasa"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

type AdminHandler struct {
	customerRepository   *database.CustomerRepository
	purchaseRepository   *database.PurchaseRepository
	cryptoPayClient      *cryptopay.Client
	yookasaClient        *yookasa.Client
	translation          *translation.Manager
	paymentService       *payment.PaymentService
	syncService          *sync.SyncService
	referralRepository   *database.ReferralRepository
	cache                *cache.Cache
	broadcastTaskService *service.BroadcastTaskService
	BroadcastWakeup      chan struct{}
	tariffRepository     *database.TariffRepository
}

func NewAdminHandler(
	syncService *sync.SyncService,
	paymentService *payment.PaymentService,
	translation *translation.Manager,
	customerRepository *database.CustomerRepository,
	purchaseRepository *database.PurchaseRepository,
	cryptoPayClient *cryptopay.Client,
	yookasaClient *yookasa.Client,
	referralRepository *database.ReferralRepository,
	cache *cache.Cache,
	broadcastTaskService *service.BroadcastTaskService,
	broadcastWakeup chan struct{},
	tariffRepository *database.TariffRepository,
) *AdminHandler {
	return &AdminHandler{
		syncService:          syncService,
		paymentService:       paymentService,
		customerRepository:   customerRepository,
		purchaseRepository:   purchaseRepository,
		cryptoPayClient:      cryptoPayClient,
		yookasaClient:        yookasaClient,
		translation:          translation,
		referralRepository:   referralRepository,
		cache:                cache,
		broadcastTaskService: broadcastTaskService,
		BroadcastWakeup:      broadcastWakeup,
		tariffRepository:     tariffRepository,
	}
}

func (h *AdminHandler) AdminCommandHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	if update.CallbackQuery != nil && update.CallbackQuery.Message.Message != nil {
		text, keyboard := buildAdminMenu()
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    update.CallbackQuery.Message.Message.Chat.ID,
			MessageID: update.CallbackQuery.Message.Message.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка обновления админ-меню (callback)", err)
		} else {
			h.cache.SetInt(9999999+update.CallbackQuery.From.ID, msgEdit.ID)
		}
		return
	}
	if update.Message == nil {
		slog.Warn("AdminCommandHandler: update.Message is nil", "update", update)
		return
	}
	slog.Info("AdminCommandHandler called", "user_id", update.Message.From.ID, "username", update.Message.From.Username, "text", update.Message.Text)
	if update.Message.From.ID != config.GetAdminTelegramId() {
		slog.Warn("AdminCommandHandler: not admin", "user_id", update.Message.From.ID, "admin_id", config.GetAdminTelegramId())
		return
	}
	text, keyboard := buildAdminMenu()
	msgReply, err := b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:    update.Message.Chat.ID,
		Text:      text,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: keyboard,
		},
	})
	if err != nil {
		slog.Error("Ошибка отправки админ-меню", err)
	} else {
		h.cache.SetInt(9999999+update.Message.From.ID, msgReply.ID)
	}
}

func buildAdminMenu() (string, [][]models.InlineKeyboardButton) {
	return "<b>Админ-меню:</b>", [][]models.InlineKeyboardButton{
		{{Text: "🔄 Синхронизация", CallbackData: "admin_sync"}},
		{{Text: "📢 Рассылка", CallbackData: "admin_broadcast"}},
		{{Text: "💸 Тарифы", CallbackData: "admin_tariffs"}},
		{{Text: "🎟️ Промокоды", CallbackData: "admin_promocodes"}},
	}
}
