package handler

import (
	"fmt"
	"remnawave-tg-shop-bot/internal/config"
	"time"
)

func FormatTimeWithTZ(t time.Time) string {
	loc, err := time.LoadLocation(config.TimeZone())
	if err != nil {
		loc = time.FixedZone("UTC+3", 3*60*60) // fallback
	}
	tInLoc := t.In(loc)
	_, offset := tInLoc.Zone()
	hours := offset / 3600
	var sign string
	if hours >= 0 {
		sign = "+"
	} else {
		sign = "-"
	}
	return tInLoc.Format("02.01.2006 15:04") +
		fmt.Sprintf(" (UTC%s%02d)", sign, abs(hours))
}

func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func formatCurrentTZLabel() string {
	loc, err := time.LoadLocation(config.TimeZone())
	if err != nil {
		return "UTC+03:00"
	}
	t := time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, loc)
	_, offset := t.Zone()
	hours := offset / 3600
	minutes := (offset % 3600) / 60
	return fmt.Sprintf("UTC%+03d:%02d", hours, minutes)
}

func FormatTimeWithTZMustParse(timeStr string) string {
	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return "не удалось распознать время"
	}
	return FormatTimeWithTZ(t)
}