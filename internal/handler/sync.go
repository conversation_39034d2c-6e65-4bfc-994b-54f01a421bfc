package handler

import (
	"context"

	"log/slog"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

func (h <PERSON>ler) SyncUsersCommandHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	h.syncService.Sync()
	var chatID int64
	if update.Message != nil {
		chatID = update.Message.Chat.ID
	} else if update.CallbackQuery != nil {
		if update.CallbackQuery.Message.Message != nil {
			chatID = update.CallbackQuery.Message.Message.Chat.ID
		} else {
			slog.Error("SyncUsersCommandHandler: CallbackQuery.Message.Message is nil", "update", update)
			return
		}
	} else {
		slog.Error("SyncUsersCommandHandler: не удалось определить chatID для ответа", "update", update)
		return
	}
	_, err := b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID: chatID,
		Text:   "Пользователи синхронизированы.",
	})
	if err != nil {
		slog.Error("Error sending sync message", err)
	}
}
