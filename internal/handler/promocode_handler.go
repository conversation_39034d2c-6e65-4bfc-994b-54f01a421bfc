package handler

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/service"
	"strings"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// FSM состояния для промокодов
const (
	promoCodeFSMStateInput = 5001 // Ввод промокода пользователем
)

// PromoCodeHandler - обработчик промокодов
type PromoCodeHandler struct {
	*AdminHandler
	promoCodeService *service.PromoCodeService
}

// NewPromoCodeHandler создает новый обработчик промокодов
func NewPromoCodeHandler(adminHandler *AdminHandler, promoCodeService *service.PromoCodeService) *PromoCodeHandler {
	return &PromoCodeHandler{
		AdminHandler:     adminHandler,
		promoCodeService: promoCodeService,
	}
}

// PromoCodeAskHandler обрабатывает запрос на использование промокода
func (h *PromoCodeHandler) PromoCodeAskHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	// Извлекаем код тарифа из callback data
	callbackQuery := parseCallbackData(update.CallbackQuery.Data)
	code := callbackQuery["code"]

	if code == "" {
		slog.Error("Отсутствует код тарифа в callback data", "data", update.CallbackQuery.Data)
		return
	}

	// Сохраняем код тарифа в кэше для дальнейшего использования
	h.cache.SetString(6000000+update.CallbackQuery.From.ID, code)

	// Показываем вопрос об использовании промокода
	text := h.translation.GetText(langCode, "promo_ask_question")
	keyboard := [][]models.InlineKeyboardButton{
		{{Text: h.translation.GetText(langCode, "promo_yes_button"), CallbackData: fmt.Sprintf("%s?code=%s", CallbackPromoCodeYes, code)}},
		{{Text: h.translation.GetText(langCode, "promo_no_button"), CallbackData: fmt.Sprintf("%s?code=%s", CallbackPromoCodeNo, code)}},
	}

	_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		Text:      text,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: keyboard,
		},
	})

	if err != nil {
		slog.Error("Ошибка отправки вопроса о промокоде", "error", err)
	}
}

// PromoCodeYesHandler обрабатывает выбор "Да" для промокода
func (h *PromoCodeHandler) PromoCodeYesHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode
	userID := update.CallbackQuery.From.ID

	// Извлекаем код тарифа из callback data
	callbackQuery := parseCallbackData(update.CallbackQuery.Data)
	code := callbackQuery["code"]

	if code == "" {
		slog.Error("Отсутствует код тарифа в callback data", "data", update.CallbackQuery.Data)
		return
	}

	// Сохраняем код тарифа в кэше для дальнейшего использования
	h.cache.SetString(6000000+userID, code)

	// Устанавливаем состояние ввода промокода
	h.cache.SetInt(userID, promoCodeFSMStateInput)

	// Показываем поле ввода промокода
	text := h.translation.GetText(langCode, "promo_input_request")
	keyboard := [][]models.InlineKeyboardButton{
		{{Text: h.translation.GetText(langCode, "promo_cancel_button"), CallbackData: CallbackPromoCodeCancel}},
	}

	_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		Text:      text,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: keyboard,
		},
	})

	if err != nil {
		slog.Error("Ошибка отправки запроса ввода промокода", "error", err)
	} else {
		// Сохраняем ID сообщения для последующего редактирования
		h.cache.SetInt(9999999+userID, callback.ID)
	}
}

// PromoCodeNoHandler обрабатывает выбор "Нет" для промокода
func (h *PromoCodeHandler) PromoCodeNoHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	userID := update.CallbackQuery.From.ID

	// Извлекаем код тарифа из callback data
	callbackQuery := parseCallbackData(update.CallbackQuery.Data)
	code := callbackQuery["code"]

	if code == "" {
		slog.Error("Отсутствует код тарифа в callback data", "data", update.CallbackQuery.Data)
		return
	}

	// Сохраняем код тарифа в кэше для дальнейшего использования
	h.cache.SetString(6000000+userID, code)

	// Переходим к выбору способа оплаты без промокода
	h.showPaymentMethods(ctx, b, update, nil)
}

// PromoCodeCancelHandler обрабатывает отмену ввода промокода
func (h *PromoCodeHandler) PromoCodeCancelHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	userID := update.CallbackQuery.From.ID

	// Сбрасываем состояние FSM
	h.cache.SetInt(userID, 0)

	// Возвращаемся к выбору тарифов - создаем новый update для BuyCallbackHandler
	newUpdate := &models.Update{
		CallbackQuery: &models.CallbackQuery{
			ID:      update.CallbackQuery.ID,
			From:    update.CallbackQuery.From,
			Message: update.CallbackQuery.Message,
			Data:    CallbackBuy,
		},
	}

	// Создаем временный handler для вызова BuyCallbackHandler
	tempHandler := Handler{AdminHandler: h.AdminHandler}
	tempHandler.BuyCallbackHandler(ctx, b, newUpdate)
}

// PromoCodeInputHandler обрабатывает ввод промокода пользователем
func (h *PromoCodeHandler) PromoCodeInputHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	userID := update.Message.From.ID
	promoCode := strings.TrimSpace(update.Message.Text)

	// Проверяем состояние FSM
	state, ok := h.cache.GetInt(userID)
	if !ok || state != promoCodeFSMStateInput {
		return
	}

	// Получаем пользователя
	customer, err := h.customerRepository.FindByTelegramId(ctx, userID)
	if err != nil || customer == nil {
		slog.Error("Ошибка получения пользователя", "error", err, "userID", userID)
		return
	}

	// Удаляем сообщение пользователя с промокодом
	b.DeleteMessage(ctx, &bot.DeleteMessageParams{
		ChatID:    update.Message.Chat.ID,
		MessageID: update.Message.ID,
	})

	// Валидируем промокод (базовая проверка без тарифа)
	validPromoCode, err := h.promoCodeService.GetPromoCodeByCode(ctx, promoCode)
	if err != nil || validPromoCode == nil {
		// Показываем ошибку - промокод не найден
		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "🔄 Попробовать снова", CallbackData: "buy_promo_yes"}},
			{{Text: "❌ Отмена", CallbackData: CallbackBuy}},
		}

		// Получаем ID сообщения для редактирования
		msgID, ok := h.cache.GetInt(9999999 + userID)
		if ok {
			_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: msgID,
				Text:      "❌ Промокод не найден или неактивен",
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: keyboard,
				},
			})
		} else {
			// Если нет ID сообщения, отправляем новое
			_, err = b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID:    update.Message.Chat.ID,
				Text:      "❌ Промокод не найден или неактивен",
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: keyboard,
				},
			})
		}

		if err != nil {
			slog.Error("Ошибка отправки сообщения об ошибке промокода", "error", err)
		}

		// Сбрасываем состояние FSM
		h.cache.SetInt(userID, 0)
		return
	}

	// Промокод найден, сбрасываем состояние FSM
	h.cache.SetInt(userID, 0)

	// Показываем тарифы с примененным промокодом
	h.showTariffsWithPromoCode(ctx, b, update, promoCode, validPromoCode.DiscountPercent)
}

// showTariffsWithPromoCode показывает тарифы с примененным промокодом
func (h *PromoCodeHandler) showTariffsWithPromoCode(ctx context.Context, b *bot.Bot, update *models.Update, promoCode string, discountPercent int) {
	langCode := update.Message.From.LanguageCode

	// Получаем тарифы из БД
	tariffs, err := h.tariffRepository.GetAll(ctx, true)
	if err != nil {
		slog.Error("Ошибка получения тарифов", "error", err)
		return
	}

	if len(tariffs) == 0 {
		_, _ = b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID: update.Message.Chat.ID,
			Text:   "❌ Нет доступных тарифов",
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackStart}},
				},
			},
		})
		return
	}

	var priceButtons []models.InlineKeyboardButton

	// Формируем кнопки тарифов со скидкой
	for _, tariff := range tariffs {
		// Рассчитываем цены со скидкой
		_, discountedPriceRUB := h.promoCodeService.CalculateDiscountedPrice(tariff.PriceRUB, discountPercent, "RUB")
		_, discountedPriceStars := h.promoCodeService.CalculateDiscountedPrice(tariff.PriceStars, discountPercent, "STARS")

		// Сокращенный текст кнопки с промокодом
		buttonText := fmt.Sprintf("%s -%d%% 🎉 (%d₽/%d⭐)",
			tariff.Title, discountPercent, discountedPriceRUB, discountedPriceStars)

		callbackData := fmt.Sprintf("%s?code=%s&promo=%s", CallbackSell, tariff.Code, promoCode)

		btn := models.InlineKeyboardButton{
			Text:         buttonText,
			CallbackData: callbackData,
		}
		priceButtons = append(priceButtons, btn)
	}

	// Формируем клавиатуру
	keyboard := [][]models.InlineKeyboardButton{}

	if len(priceButtons) == 4 {
		keyboard = append(keyboard, priceButtons[:2])
		keyboard = append(keyboard, priceButtons[2:])
	} else if len(priceButtons) > 0 {
		keyboard = append(keyboard, priceButtons)
	}

	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackBuy},
	})

	// Формируем текст сообщения
	text := fmt.Sprintf("✅ Промокод применен! Скидка: <b>%d%%</b>\n\n%s",
		discountPercent, h.translation.GetText(langCode, "pricing_info"))

	// Получаем ID сообщения для редактирования
	userID := update.Message.From.ID
	msgID, ok := h.cache.GetInt(9999999 + userID)
	if !ok {
		// Если ID сообщения не найден, отправляем новое сообщение
		_, err = b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    update.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
	} else {
		// Редактируем существующее сообщение
		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: msgID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
	}

	if err != nil {
		slog.Error("Ошибка показа тарифов с промокодом", "error", err)
	}
}

// PromoCodeApplyHandler обрабатывает применение промокода и переход к оплате
func (h *PromoCodeHandler) PromoCodeApplyHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	userID := update.CallbackQuery.From.ID

	// Получаем промокод из кэша
	promoCode, ok := h.cache.GetString(6000001 + userID)
	if !ok {
		slog.Error("Отсутствует промокод в кэше", "userID", userID)
		return
	}

	// Переходим к выбору способа оплаты с промокодом
	h.showPaymentMethods(ctx, b, update, &promoCode)
}

// showPaymentMethods показывает способы оплаты (с промокодом или без)
func (h *PromoCodeHandler) showPaymentMethods(ctx context.Context, b *bot.Bot, update *models.Update, promoCode *string) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode
	userID := update.CallbackQuery.From.ID

	// Получаем код тарифа из кэша
	tariffCode, ok := h.cache.GetString(6000000 + userID)
	if !ok {
		slog.Error("Отсутствует код тарифа в кэше", "userID", userID)
		return
	}

	// Получаем тариф
	tariff, err := h.tariffRepository.GetByCode(ctx, tariffCode)
	if err != nil || tariff == nil {
		slog.Error("Ошибка получения тарифа", "error", err, "code", tariffCode)
		return
	}

	// Рассчитываем цены (с промокодом или без)
	var priceRUB, priceStars int
	var discountText string

	if promoCode != nil {
		// Получаем пользователя
		customer, err := h.customerRepository.FindByTelegramId(ctx, userID)
		if err != nil || customer == nil {
			slog.Error("Ошибка получения пользователя", "error", err, "userID", userID)
			return
		}

		// Получаем промокод из БД
		validPromoCode, err := h.promoCodeService.GetPromoCodeByCode(ctx, *promoCode)
		if err != nil || validPromoCode == nil {
			slog.Error("Ошибка получения промокода", "error", err, "code", *promoCode)
			return
		}

		// Рассчитываем цены со скидкой
		_, priceRUB = h.promoCodeService.CalculateDiscountedPrice(tariff.PriceRUB, validPromoCode.DiscountPercent, "RUB")
		_, priceStars = h.promoCodeService.CalculateDiscountedPrice(tariff.PriceStars, validPromoCode.DiscountPercent, "STARS")

		discountText = fmt.Sprintf(h.translation.GetText(langCode, "promo_discount_applied"), validPromoCode.DiscountPercent)
	} else {
		priceRUB = tariff.PriceRUB
		priceStars = tariff.PriceStars
	}

	// Формируем клавиатуру с способами оплаты аналогично SellCallbackHandler
	var keyboard [][]models.InlineKeyboardButton

	// Добавляем способы оплаты с проверкой доступности
	if config.IsYookasaEnabled() && priceRUB > 0 {
		cardText := fmt.Sprintf(h.translation.GetText(langCode, "card_button"), priceRUB)
		callbackData := fmt.Sprintf("%s?code=%s", CallbackPayment, tariffCode)
		if promoCode != nil {
			callbackData += "&promo=" + *promoCode
		}
		callbackData += "&invoiceType=" + string(database.InvoiceTypeYookasa)
		keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: cardText, CallbackData: callbackData}})
	}

	if config.IsTelegramStarsEnabled() && priceStars > 0 {
		starsText := fmt.Sprintf(h.translation.GetText(langCode, "stars_button"), priceStars)
		callbackData := fmt.Sprintf("%s?code=%s", CallbackPayment, tariffCode)
		if promoCode != nil {
			callbackData += "&promo=" + *promoCode
		}
		callbackData += "&invoiceType=" + string(database.InvoiceTypeTelegram)
		keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: starsText, CallbackData: callbackData}})
	}

	if config.IsCryptoPayEnabled() && priceRUB > 0 {
		cryptoText := fmt.Sprintf(h.translation.GetText(langCode, "crypto_button"), priceRUB)
		callbackData := fmt.Sprintf("%s?code=%s", CallbackPayment, tariffCode)
		if promoCode != nil {
			callbackData += "&promo=" + *promoCode
		}
		callbackData += "&invoiceType=" + string(database.InvoiceTypeCrypto)
		keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: cryptoText, CallbackData: callbackData}})
	}

	if config.GetTributeWebHookUrl() != "" {
		keyboard = append(keyboard, []models.InlineKeyboardButton{
			{Text: h.translation.GetText(langCode, "tribute_button"), URL: config.GetTributePaymentUrl()},
		})
	}

	// Кнопка "Назад"
	keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackBuy}})

	// Формируем текст сообщения
	text := fmt.Sprintf(h.translation.GetText(langCode, "payment_methods_title"), tariff.Title)
	if discountText != "" {
		text += "\n\n" + discountText
	}

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		Text:      text,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: keyboard,
		},
	})

	if err != nil {
		slog.Error("Ошибка отправки способов оплаты", "error", err)
	}
}
