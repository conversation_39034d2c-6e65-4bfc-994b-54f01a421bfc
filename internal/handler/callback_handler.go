package handler

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/database"
	"strconv"
	"strings"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

type CallbackHandler struct {
	*AdminHandler
	*BroadcastHandler
	*TariffHandler
	*PromoCodeAdminHandler
}

func NewCallbackHandler(adminHandler *AdminHandler, broadcastHandler *BroadcastHandler, tariffHandler *TariffHandler, promoCodeAdminHandler *PromoCodeAdminHandler) *CallbackHandler {
	return &CallbackHandler{AdminHandler: admin<PERSON><PERSON>ler, BroadcastHandler: broadcastHandler, TariffHandler: tariff<PERSON>andler, PromoCodeAdminHandler: promoCodeAdminHandler}
}

func (h *CallbackHandler) AdminCallbackHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	if update.CallbackQuery == nil || (!strings.HasPrefix(update.CallbackQuery.Data, "admin_") && !strings.HasPrefix(update.CallbackQuery.Data, "tariff_") && !strings.HasPrefix(update.CallbackQuery.Data, "broadcast_") && !strings.HasPrefix(update.CallbackQuery.Data, "instant_broadcast") && !strings.HasPrefix(update.CallbackQuery.Data, "promocode_") && !strings.HasPrefix(update.CallbackQuery.Data, "promo_")) {
		return
	}
	var state int
	if update.CallbackQuery != nil {
		st, ok := h.cache.GetInt(update.CallbackQuery.From.ID)
		if ok {
			state = st
		}
		slog.Info("[AdminCallbackHandler]", "user_id", update.CallbackQuery.From.ID, "state", state, "callback_data", update.CallbackQuery.Data)
	}
	if update == nil {
		slog.Error("[AdminCallbackHandler] update is nil")
		return
	}
	if update.CallbackQuery == nil {
		slog.Error("[AdminCallbackHandler] CallbackQuery is nil", "update", update)
		return
	}
	callback := update.CallbackQuery
	if callback.From.ID == 0 {
		slog.Error("[AdminCallbackHandler] CallbackQuery.From.ID is zero", "callback", callback)
		return
	}
	if callback.Message.Message == nil {
		slog.Error("[AdminCallbackHandler] CallbackQuery.Message.Message is nil", "callback", callback)
		return
	}
	if callback.From.ID != config.GetAdminTelegramId() {
		return
	}

	switch callback.Data {
	case "admin_menu":
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
		})
		h.clearTariffMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.SetString(1000000+callback.From.ID, "")
		h.cache.SetString(2000000+callback.From.ID, "")
		h.cache.SetString(3000000+callback.From.ID, "")
		h.cache.SetString(4000000+callback.From.ID, "")
		text, keyboard := buildAdminMenu()
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка отправки админ-меню (callback)", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msg.ID)
		}
		return
	case "admin_broadcast":
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
		})
		h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.SetString(1000000+callback.From.ID, "")
		h.cache.SetString(2000000+callback.From.ID, "")
		h.cache.SetString(3000000+callback.From.ID, "")
		h.cache.SetString(4000000+callback.From.ID, "")
		text, keyboard := buildBroadcastMenu()
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка отправки меню рассылок", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msg.ID)
		}
		return
	case "instant_broadcast":
		// Очищаем предыдущие сообщения для динамического скрытия меню
		h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)

		h.cache.SetInt(callback.From.ID, adminBroadcastStateInstantText)
		h.cache.SetString(1000000+callback.From.ID, "")
		h.cache.SetString(2000000+callback.From.ID, "")
		h.cache.SetString(3000000+callback.From.ID, "")
		h.cache.SetString(4000000+callback.From.ID, "")
		text, keyboard := buildInstantBroadcastMenu()

		// Используем SendMessage вместо EditMessageText, так как предыдущие сообщения были удалены
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка запуска моментальной рассылки", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msg.ID)
		}
		return
	case "admin_sync":
		h.syncService.Sync()
		return
	case "admin_promocodes":
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
		})
		h.clearTariffMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.clearPromoCodeMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.cache.SetInt(callback.From.ID, 0)

		// Вызываем метод показа промокодов из PromoCodeAdminHandler
		if h.PromoCodeAdminHandler != nil {
			h.PromoCodeAdminHandler.showPromoCodesPage(ctx, b, callback, 1)
		}
		return
	case "broadcast_list":
		h.sendPaginatedBroadcastList(ctx, b, callback, 1)
		return
	case "broadcast_create":
		// Очищаем предыдущие сообщения для динамического скрытия меню
		h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)

		h.cache.SetInt(callback.From.ID, adminBroadcastStateCreateText)
		h.cache.SetString(1000000+callback.From.ID, "")
		h.cache.SetString(2000000+callback.From.ID, "")
		text, keyboard := buildCreateBroadcastMenu()

		// Используем SendMessage вместо EditMessageText, так как предыдущие сообщения были удалены
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка отправки меню создания рассылки", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msg.ID)
		}
		return
	case "broadcast_create_cancel":
		h.sendPaginatedBroadcastList(ctx, b, callback, 1)
		return
	case "instant_broadcast_send_all", "instant_broadcast_send_unsubscribed":
		text, _ := h.cache.GetString(9000000 + callback.From.ID)
		var users []database.Customer
		var err error
		if callback.Data == "instant_broadcast_send_all" {
			users, err = h.customerRepository.GetAll(ctx)
		} else {
			users, err = h.customerRepository.GetUsersWithoutActiveSubscription(ctx)
		}

		targetAudience := "all"
		if callback.Data == "instant_broadcast_send_unsubscribed" {
			targetAudience = "unsubscribed"
		}

		if err != nil {
			b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Ошибка выборки пользователей.",
			})
			return
		}
		var success, failed int
		var blocked []int64
		for _, user := range users {
			if user.TelegramID == config.GetAdminTelegramId() {
				continue
			}
			_, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID: user.TelegramID,
				Text:   text,
			})
			if err != nil {
				failed++
				blocked = append(blocked, user.TelegramID)
			} else {
				success++
			}
			time.Sleep(35 * time.Millisecond)
		}
		now := time.Now()
		msgText := fmt.Sprintf("📢 <b>Рассылка завершена!</b>\n\nТекст: %s\nАудитория: %s\nВремя отправки: %s\n\n✅ Успешно: <b>%d</b>\n🚫 Ошибок: <b>%d</b>", text, TranslateAudience(targetAudience), FormatTimeWithTZ(now), success, failed)
		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "⬅️ В меню рассылок", CallbackData: "admin_broadcast"}},
		}
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
			Text:      msgText,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка обновления сообщения о результатах рассылки", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
		}
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.SetString(9000000+callback.From.ID, "")
		return
	case "instant_broadcast_cancel":
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
		})
		text, keyboard := buildBroadcastMenu()
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка возврата к меню рассылок", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msg.ID)
		}
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.SetString(9000000+callback.From.ID, "")
		return
	case "admin_tariffs":
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
		})
		// Очищаем предыдущие сообщения меню тарифов перед показом обновленного списка
		h.clearTariffMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.showTariffsPage(ctx, b, callback, 1)
		return
	case "tariff_add":
		h.cache.SetInt(callback.From.ID, tariffFSMStateCode)
		h.cache.SetString(7000000+callback.From.ID, "")
		h.cache.SetString(7000001+callback.From.ID, "")
		h.cache.SetString(7000002+callback.From.ID, "")
		h.cache.SetString(7000003+callback.From.ID, "")
		h.cache.SetString(7000004+callback.From.ID, "")
		var chatID int64
		if callback.Message.Message != nil {
			chatID = callback.Message.Message.Chat.ID
			h.clearTariffMessages(ctx, b, callback.From.ID, chatID)
		} else {
			chatID = callback.From.ID
		}
		slog.Info("[TARIFF FSM] Отправляю приглашение к вводу кода тарифа", "chatID", chatID, "userID", callback.From.ID)
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    chatID,
			Text:      "Введите <b>код тарифа</b> (латиница, цифры, например: 1m, 3m, basic):",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("[TARIFF FSM] Ошибка вывода шага ввода кода тарифа", "err", err, "chatID", chatID, "userID", callback.From.ID)
			return
		}
		h.cache.SetInt(9999999+callback.From.ID, msg.ID)
		return
	case "tariff_create":
		code, _ := h.cache.GetString(7000000 + callback.From.ID)
		title, _ := h.cache.GetString(7000001 + callback.From.ID)
		priceRUB, _ := h.cache.GetInt(7000002 + callback.From.ID)
		priceStars, _ := h.cache.GetInt(7000003 + callback.From.ID)
		tariff := &database.Tariff{
			Code:       code,
			Title:      title,
			PriceRUB:   priceRUB,
			PriceStars: priceStars,
			Active:     false,
		}
		_, err := h.tariffRepository.Create(ctx, tariff)
		if err != nil {
			msg := callback.Message.Message
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
				Text:      "Ошибка создания тарифа.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		msg := callback.Message.Message
		_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
			Text:      "✅ Тариф успешно создан и пока неактивен. Для активации используйте соответствующую кнопку в списке тарифов.",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "📋 В меню тарифов", CallbackData: "admin_tariffs"}},
				},
			},
		})
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.Delete(7000000 + callback.From.ID)
		h.cache.Delete(7000001 + callback.From.ID)
		h.cache.Delete(7000002 + callback.From.ID)
		h.cache.Delete(7000003 + callback.From.ID)
		return
	case "tariff_update":
		code, _ := h.cache.GetString(7000000 + callback.From.ID)
		title, _ := h.cache.GetString(7000001 + callback.From.ID)
		priceRUB, _ := h.cache.GetInt(7000002 + callback.From.ID)
		priceStars, _ := h.cache.GetInt(7000003 + callback.From.ID)

		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil {
			msg := callback.Message.Message
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
				Text:      "Ошибка обновления тарифа.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}

		tariff.Title = title
		tariff.PriceRUB = priceRUB
		tariff.PriceStars = priceStars

		err = h.tariffRepository.Update(ctx, tariff)
		if err != nil {
			msg := callback.Message.Message
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
				Text:      "Ошибка обновления тарифа.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}

		msg := callback.Message.Message
		_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
			Text:      "✅ Тариф успешно обновлен.",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "📋 В меню тарифов", CallbackData: "admin_tariffs"}},
				},
			},
		})
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.Delete(7000000 + callback.From.ID)
		h.cache.Delete(7000001 + callback.From.ID)
		h.cache.Delete(7000002 + callback.From.ID)
		h.cache.Delete(7000003 + callback.From.ID)
		return
	}

	switch {
	case strings.HasPrefix(callback.Data, "broadcast_list_page_"):
		page := 1
		pstr := strings.TrimPrefix(callback.Data, "broadcast_list_page_")
		if n, err := strconv.Atoi(pstr); err == nil && n > 0 {
			page = n
		}
		h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.sendPaginatedBroadcastList(ctx, b, callback, page)
		return
	case strings.HasPrefix(callback.Data, "broadcast_create_confirm_"):
		if state, ok := h.cache.GetInt(callback.From.ID); !ok || state != adminBroadcastStateCreateReady {
			return
		}
		targetAudience := strings.TrimPrefix(callback.Data, "broadcast_create_confirm_")

		text, _ := h.cache.GetString(1000000 + callback.From.ID)
		timeStr, _ := h.cache.GetString(2000000 + callback.From.ID)
		t, err := time.Parse(time.RFC3339, timeStr)
		if err != nil {
			b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Ошибка парсинга времени. Операция отменена.",
			})
			return
		}
		task := &database.BroadcastTask{
			Message:        text,
			SendAt:         t.UTC(),
			Status:         database.BroadcastTaskStatusPending,
			TargetAudience: targetAudience,
		}
		_, err = h.broadcastTaskService.Create(ctx, task)
		if err != nil {
			b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Ошибка создания рассылки.",
			})
			return
		}
		if h.BroadcastWakeup != nil {
			select {
			case h.BroadcastWakeup <- struct{}{}:
			default:
			}
		}
		textMenu, keyboard := buildSuccessBroadcastCreatedMenu(targetAudience)
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
			Text:      textMenu,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка отправки сообщения об успешном создании рассылки", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
		}
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.SetString(1000000+callback.From.ID, "")
		h.cache.SetString(2000000+callback.From.ID, "")
		return
	case strings.HasPrefix(callback.Data, "broadcast_delete_confirm_"):
		slog.Info("DELETE_CONFIRM: Handling callback", "data", callback.Data)
		idStr := strings.TrimPrefix(strings.SplitN(callback.Data, "_p", 2)[0], "broadcast_delete_confirm_")
		page := 1
		if strings.Contains(callback.Data, "_p") {
			pstr := strings.SplitN(callback.Data, "_p", 2)[1]
			if n, err := strconv.Atoi(pstr); err == nil && n > 0 {
				page = n
			}
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			slog.Error("DELETE_CONFIRM: Failed to parse ID", "idStr", idStr, "error", err)
			return
		}

		slog.Info("DELETE_CONFIRM: Attempting to delete task", "id", id)
		err = h.broadcastTaskService.Delete(ctx, id)
		if err != nil {
			slog.Error("DELETE_CONFIRM: Error deleting broadcast task", "err", err, "task_id", id)
			b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "❌ Ошибка при удалении рассылки.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{
							{Text: "⬅️ К списку", CallbackData: fmt.Sprintf("broadcast_list_page_%d", page)},
						},
					},
				},
			})
			return
		}
		slog.Info("DELETE_CONFIRM: Task deleted successfully", "id", id)

		if h.BroadcastWakeup != nil {
			select {
			case h.BroadcastWakeup <- struct{}{}:
			default:
			}
		}
		// Очищаем предыдущие сообщения перед отображением обновленного списка
		h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.sendPaginatedBroadcastList(ctx, b, callback, page)
		return
	case strings.HasPrefix(callback.Data, "broadcast_edit_confirm_"):
		idStr := strings.TrimPrefix(strings.SplitN(callback.Data, "_p", 2)[0], "broadcast_edit_confirm_")
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			return
		}
		text, _ := h.cache.GetString(3000000 + callback.From.ID)
		timeStr, _ := h.cache.GetString(4000000 + callback.From.ID)
		t, err := time.Parse(time.RFC3339, timeStr)
		if err != nil {
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Ошибка парсинга времени.",
			})
			if err != nil {
				slog.Error("Ошибка обновления сообщения об ошибке парсинга времени", err)
			} else {
				h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
			}
			return
		}
		task, err := h.broadcastTaskService.GetByID(ctx, id)
		if err != nil || task == nil {
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Задача не найдена.",
			})
			if err != nil {
				slog.Error("Ошибка обновления сообщения об ошибке поиска задачи", err)
			} else {
				h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
			}
			return
		}
		task.Message = text
		task.SendAt = t.UTC()
		err = h.broadcastTaskService.Delete(ctx, id)
		if err == nil {
			_, err = h.broadcastTaskService.Create(ctx, task)
		}
		if err != nil {
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Ошибка сохранения изменений.",
			})
			if err != nil {
				slog.Error("Ошибка обновления сообщения об ошибке сохранения изменений", err)
			} else {
				h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
			}
		} else {
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "✅ Изменения успешно сохранены!",
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ В меню рассылок", CallbackData: "admin_broadcast"}},
					},
				},
			})
			if err != nil {
				slog.Error("Ошибка обновления сообщения об успешном сохранении изменений", err)
			} else {
				h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
			}
			if h.BroadcastWakeup != nil {
				select {
				case h.BroadcastWakeup <- struct{}{}:
				default:
				}
			}
		}
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.SetString(3000000+callback.From.ID, "")
		h.cache.SetString(4000000+callback.From.ID, "")
		h.cache.Delete(7000000 + callback.From.ID)
		return
	case strings.HasPrefix(callback.Data, "broadcast_delete_"):
		idStr := strings.TrimPrefix(strings.SplitN(callback.Data, "_p", 2)[0], "broadcast_delete_")
		page := 1
		if strings.Contains(callback.Data, "_p") {
			pstr := strings.SplitN(callback.Data, "_p", 2)[1]
			if n, err := strconv.Atoi(pstr); err == nil && n > 0 {
				page = n
			}
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			return
		}
		msg, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
			Text:      "Удалить рассылку?",
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "Да", CallbackData: fmt.Sprintf("broadcast_delete_confirm_%d_p%d", id, page)}},
					{{Text: "Нет", CallbackData: fmt.Sprintf("broadcast_revert_%d_p%d", id, page)}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка отправки сообщения для подтверждения удаления рассылки", "err", err)
		} else if msg != nil {
			h.cache.Set(6000000+callback.From.ID, msg.ID)
		}
		return
	case strings.HasPrefix(callback.Data, "broadcast_revert_"):
		idStr := strings.TrimPrefix(strings.SplitN(callback.Data, "_p", 2)[0], "broadcast_revert_")
		page := 1
		if strings.Contains(callback.Data, "_p") {
			pstr := strings.SplitN(callback.Data, "_p", 2)[1]
			if n, err := strconv.Atoi(pstr); err == nil && n > 0 {
				page = n
			}
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			return
		}
		task, err := h.broadcastTaskService.GetByID(ctx, id)
		if err != nil {
			return
		}
		text, keyboard := buildSingleBroadcastEntry(*task, page)
		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:      callback.Message.Message.Chat.ID,
			MessageID:   callback.Message.Message.ID,
			Text:        text,
			ParseMode:   models.ParseModeHTML,
			ReplyMarkup: keyboard,
		})
		if err != nil {
			slog.Error("Ошибка возврата к сообщению о рассылке", err)
		}
		return
	case strings.HasPrefix(callback.Data, "broadcast_send_"):
		idStr := strings.TrimPrefix(strings.SplitN(callback.Data, "_p", 2)[0], "broadcast_send_")
		page := 1
		if strings.Contains(callback.Data, "_p") {
			pstr := strings.SplitN(callback.Data, "_p", 2)[1]
			if n, err := strconv.Atoi(pstr); err == nil && n > 0 {
				page = n
			}
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			return
		}
		task, err := h.broadcastTaskService.GetByID(ctx, id)
		if err != nil || task == nil {
			msgReply, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID: callback.Message.Message.Chat.ID,
				Text:   "Задача не найдена.",
			})
			if err != nil {
				slog.Error("Ошибка отправки сообщения об ошибке поиска задачи", err)
			} else if msgReply != nil {
				h.cache.SetInt(9999999+callback.From.ID, msgReply.ID)
			}
			return
		}
		users, err := h.customerRepository.GetAll(ctx)
		if err != nil {
			msgReply, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID: callback.Message.Message.Chat.ID,
				Text:   "Ошибка выборки пользователей.",
			})
			if err != nil {
				slog.Error("Ошибка отправки сообщения об ошибке выборки пользователей", err)
			} else if msgReply != nil {
				h.cache.SetInt(9999999+callback.From.ID, msgReply.ID)
			}
			return
		}
		var success, failed int
		for _, user := range users {
			if user.TelegramID == config.GetAdminTelegramId() {
				continue
			}
			_, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID: user.TelegramID,
				Text:   task.Message,
			})
			if err != nil {
				failed++
			} else {
				success++
			}
			time.Sleep(35 * time.Millisecond)
		}
		msgText := fmt.Sprintf("📢 <b>Отложенная рассылка завершена!</b>\n\nID: %d\nТекст: %s\nАудитория: %s\nВремя (запланировано): %s\nВремя (отправлено): %s\n\n✅ Успешно: <b>%d</b>\n🚫 Ошибок: <b>%d</b>", task.ID, task.Message, TranslateAudience(task.TargetAudience), FormatTimeWithTZ(task.SendAt), FormatTimeWithTZ(time.Now()), success, failed)
		msgReply, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      msgText,
			ParseMode: models.ParseModeHTML,
		})
		if err != nil {
			slog.Error("Ошибка отправки сообщения об успешной отправке рассылки", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msgReply.ID)
		}
		err = h.broadcastTaskService.UpdateStatus(ctx, task.ID, database.BroadcastTaskStatusSent)
		if err != nil {
			slog.Error("Ошибка обновления статуса рассылки на 'Отправлено'", err)
		}
		h.AdminCallbackHandler(ctx, b, &models.Update{CallbackQuery: &models.CallbackQuery{From: callback.From, Message: callback.Message, Data: fmt.Sprintf("broadcast_list_page_%d", page)}})
		return
	case strings.HasPrefix(callback.Data, "broadcast_run_now_"):
		idStr := strings.TrimPrefix(strings.SplitN(callback.Data, "_p", 2)[0], "broadcast_run_now_")
		page := 1
		if strings.Contains(callback.Data, "_p") {
			pstr := strings.SplitN(callback.Data, "_p", 2)[1]
			if n, err := strconv.Atoi(pstr); err == nil && n > 0 {
				page = n
			}
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			return
		}

		// Получаем рассылку для проверки статуса
		task, err := h.broadcastTaskService.GetByID(ctx, id)
		if err != nil || task == nil {
			return
		}

		// Проверяем что рассылка в статусе ожидания
		if task.Status != database.BroadcastTaskStatusPending {
			return
		}

		// Запускаем рассылку немедленно
		var users []database.Customer
		if task.TargetAudience == "all" {
			users, err = h.customerRepository.GetAll(ctx)
		} else {
			users, err = h.customerRepository.GetUsersWithoutActiveSubscription(ctx)
		}

		if err != nil {
			return
		}

		// Отправляем рассылку всем пользователям
		for _, user := range users {
			b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID: user.TelegramID,
				Text:   task.Message,
			})
		}

		// Обновляем статус рассылки на "Отправлено"
		err = h.broadcastTaskService.UpdateStatus(ctx, task.ID, database.BroadcastTaskStatusSent)
		if err != nil {
			slog.Error("Ошибка обновления статуса рассылки на 'Отправлено'", err)
		}

		// Возвращаемся к списку рассылок
		h.AdminCallbackHandler(ctx, b, &models.Update{CallbackQuery: &models.CallbackQuery{From: callback.From, Message: callback.Message, Data: fmt.Sprintf("broadcast_list_page_%d", page)}})
		return
	case strings.HasPrefix(callback.Data, "broadcast_edit_"):
		idStr := strings.TrimPrefix(strings.SplitN(callback.Data, "_p", 2)[0], "broadcast_edit_")
		page := 1
		if strings.Contains(callback.Data, "_p") {
			pstr := strings.SplitN(callback.Data, "_p", 2)[1]
			if n, err := strconv.Atoi(pstr); err == nil && n > 0 {
				page = n
			}
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			return
		}
		task, err := h.broadcastTaskService.GetByID(ctx, id)
		if err != nil || task == nil {
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Задача не найдена.",
			})
			if err != nil {
				slog.Error("Ошибка обновления сообщения об ошибке поиска задачи", err)
			} else {
				h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
			}
			return
		}
		h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.cache.SetInt(callback.From.ID, adminBroadcastStateEditText)
		h.cache.SetInt(7000000+callback.From.ID, int(id))
		h.cache.SetString(3000000+callback.From.ID, task.Message)
		h.cache.SetString(4000000+callback.From.ID, task.SendAt.Format(time.RFC3339))
		h.cache.SetString(1000000+callback.From.ID, "")
		h.cache.SetString(2000000+callback.From.ID, "")
		slog.Info("[AdminCallbackHandler] переход к редактированию", "user_id", callback.From.ID, "state", adminBroadcastStateEditText, "callback_data", callback.Data)
		text, keyboard := buildEditBroadcastMenu(task, page)
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка отправки меню редактирования рассылки", err)
		} else if msg != nil {
			h.cache.SetInt(9999999+callback.From.ID, msg.ID)
		}
		return
	case strings.HasPrefix(callback.Data, "tariff_toggle_"):
		data := strings.TrimPrefix(callback.Data, "tariff_toggle_")
		code := data
		if idx := strings.LastIndex(data, "_p"); idx != -1 {
			code = data[:idx]
		}
		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil || tariff == nil {
			msg := callback.Message.Message
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
				Text:      "Ошибка: тариф не найден.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		if tariff.Active {
			tariffs, err := h.tariffRepository.GetAll(ctx, true)
			if err != nil {
				msg := callback.Message.Message
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msg.ID,
					Text:      "Ошибка получения списка тарифов.",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			if len(tariffs) == 1 && tariffs[0].ID == tariff.ID {
				msg := callback.Message.Message
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msg.ID,
					Text:      "Нельзя деактивировать последний активный тариф!",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			err = h.tariffRepository.SetActive(ctx, tariff.ID, false)
			if err != nil {
				msg := callback.Message.Message
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msg.ID,
					Text:      "Ошибка при изменении статуса тарифа.",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
		} else {
			err = h.tariffRepository.SetActive(ctx, tariff.ID, true)
			if err != nil {
				msg := callback.Message.Message
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msg.ID,
					Text:      "Ошибка при изменении статуса тарифа.",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
		}
		page := 1
		if idx := strings.LastIndex(callback.Data, "_p"); idx != -1 {
			if p, err := strconv.Atoi(callback.Data[idx+2:]); err == nil {
				page = p
			}
		}
		// Очищаем предыдущие сообщения перед отображением обновленного списка
		h.clearTariffMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.showTariffsPage(ctx, b, callback, page)
		return
	case strings.HasPrefix(callback.Data, "tariff_edit_"):
		data := strings.TrimPrefix(callback.Data, "tariff_edit_")
		code := data
		if idx := strings.LastIndex(data, "_p"); idx != -1 {
			code = data[:idx]
		}
		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil || tariff == nil {
			msg := callback.Message.Message
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
				Text:      "Ошибка: тариф не найден.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		h.clearTariffMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID, callback.Message.Message.ID)
		h.cache.SetInt(9999999+callback.From.ID, callback.Message.Message.ID)
		h.cache.SetString(7000000+callback.From.ID, tariff.Code)
		h.cache.SetString(7000001+callback.From.ID, tariff.Title)
		h.cache.SetInt(7000002+callback.From.ID, tariff.PriceRUB)
		h.cache.SetInt(7000003+callback.From.ID, tariff.PriceStars)
		h.cache.SetInt(7000004+callback.From.ID, boolToInt(tariff.Active))
		h.cache.SetInt(callback.From.ID, tariffFSMStateEditTitle)
		msg := callback.Message.Message
		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
			Text:      fmt.Sprintf("Редактирование тарифа <b>%s</b> (код: <code>%s</code>)\n\nВведите новое <b>название тарифа</b> или оставьте текущее:", tariff.Title, tariff.Code),
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка вывода шага редактирования названия тарифа", err)
		}
		return
	case strings.HasPrefix(callback.Data, "tariff_delete_confirm_"):
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
		})

		data := strings.TrimPrefix(callback.Data, "tariff_delete_confirm_")
		code := data
		page := 1
		if idx := strings.LastIndex(data, "_p"); idx != -1 {
			code = data[:idx]
			if p, err := strconv.Atoi(data[idx+2:]); err == nil {
				page = p
			}
		}

		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil || tariff == nil {
			h.showTariffsPage(ctx, b, callback, page)
			return
		}

		err = h.tariffRepository.Delete(ctx, tariff.ID)
		if err != nil {
			h.showTariffsPage(ctx, b, callback, page)
			return
		}

		// Очищаем предыдущие сообщения перед отображением обновленного списка
		h.clearTariffMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.showTariffsPage(ctx, b, callback, page)
		return

	case strings.HasPrefix(callback.Data, "tariff_delete_"):
		data := strings.TrimPrefix(callback.Data, "tariff_delete_")
		code := data
		page := 1
		if idx := strings.LastIndex(data, "_p"); idx != -1 {
			code = data[:idx]
			if p, err := strconv.Atoi(data[idx+2:]); err == nil {
				page = p
			}
		}

		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil || tariff == nil {
			msg := callback.Message.Message
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
				Text:      "Ошибка: тариф не найден.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад", CallbackData: fmt.Sprintf("admin_tariffs_page_%d", page)}},
					},
				},
			})
			return
		}

		msg := callback.Message.Message
		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
			Text:      fmt.Sprintf("Вы действительно хотите удалить тариф <b>%s</b> (код: <code>%s</code>)? Это действие необратимо.", tariff.Title, tariff.Code),
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{
						{Text: "🗑 Подтвердить удаление", CallbackData: fmt.Sprintf("tariff_delete_confirm_%s_p%d", tariff.Code, page)},
						{Text: "❌ Отмена", CallbackData: fmt.Sprintf("admin_tariffs_page_%d", page)},
					},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка вывода подтверждения удаления тарифа", err)
		}
		return
	case strings.HasPrefix(callback.Data, "admin_tariffs_page_"):
		pageStr := strings.TrimPrefix(callback.Data, "admin_tariffs_page_")
		page, err := strconv.Atoi(pageStr)
		if err != nil || page < 1 {
			page = 1
		}
		h.showTariffsPage(ctx, b, callback, page)
		return
	case strings.HasPrefix(callback.Data, "admin_tariffs_page_0") || (callback.Data == "admin_menu" && strings.Contains(callback.Message.Message.Text, "Тарифы")):
		h.clearTariffMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
	case strings.HasPrefix(callback.Data, "admin_promocodes_page_"):
		pageStr := strings.TrimPrefix(callback.Data, "admin_promocodes_page_")
		page, err := strconv.Atoi(pageStr)
		if err != nil || page < 1 {
			page = 1
		}
		if h.PromoCodeAdminHandler != nil {
			h.PromoCodeAdminHandler.showPromoCodesPage(ctx, b, callback, page)
		}
		return
	case callback.Data == "promocode_add":
		// Начинаем процесс создания промокода
		h.clearTariffMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.clearPromoCodeMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)

		// Устанавливаем FSM состояние для ввода кода промокода
		h.cache.SetInt(callback.From.ID, promoCodeAdminFSMStateCode)

		text := "Введите <b>код промокода</b> (только цифры и английские буквы):"
		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
		}

		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})

		if err != nil {
			slog.Error("Ошибка отправки сообщения создания промокода", "error", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msg.ID)
		}
		return
	case strings.HasPrefix(callback.Data, "promocode_toggle_"):
		// Обработка переключения статуса промокода
		h.handlePromoCodeToggle(ctx, b, callback)
		return
	case strings.HasPrefix(callback.Data, "promocode_stats_"):
		// Обработка показа статистики промокода
		h.handlePromoCodeStats(ctx, b, callback)
		return
	case strings.HasPrefix(callback.Data, "promocode_delete_"):
		// Обработка удаления промокода
		h.handlePromoCodeDelete(ctx, b, callback)
		return
	case strings.HasPrefix(callback.Data, "promo_expiry_"):
		// Обработка выбора типа срока действия
		expiryType := strings.TrimPrefix(callback.Data, "promo_expiry_")
		userID := callback.From.ID

		// Проверяем состояние FSM
		state, ok := h.cache.GetInt(userID)
		if !ok || state != promoCodeAdminFSMStateExpiryType {
			return
		}

		// Сохраняем тип срока действия
		h.cache.SetString(8000003+userID, expiryType)

		msgID, ok := h.cache.GetInt(9999999 + userID)
		if !ok {
			return
		}

		if expiryType == "end_of_day" {
			// Для "до конца дня" сразу переходим к лимиту активаций
			h.cache.SetInt(userID, promoCodeAdminFSMStateActivationLimit)
			_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: msgID,
				Text:      "Введите <b>лимит активаций</b> (0 = бесконечно):",
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
					},
				},
			})
			if err != nil {
				slog.Error("Ошибка перехода к лимиту активаций", "error", err)
			}
		} else {
			// Для часов/дней запрашиваем значение
			h.cache.SetInt(userID, promoCodeAdminFSMStateExpiryValue)
			var unit string
			if expiryType == "hours" {
				unit = "часов"
			} else {
				unit = "дней"
			}

			_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: msgID,
				Text:      fmt.Sprintf("Введите количество <b>%s</b>:", unit),
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
					},
				},
			})
			if err != nil {
				slog.Error("Ошибка запроса значения срока действия", "error", err)
			}
		}
		return

	case strings.HasPrefix(callback.Data, "promo_user_"):
		// Обработка выбора типа пользователей
		userType := strings.TrimPrefix(callback.Data, "promo_user_")
		userID := callback.From.ID

		// Проверяем состояние FSM
		state, ok := h.cache.GetInt(userID)
		if !ok || state != promoCodeAdminFSMStateUserType {
			return
		}

		// Сохраняем тип пользователей
		h.cache.SetString(8000006+userID, userType)

		msgID, ok := h.cache.GetInt(9999999 + userID)
		if !ok {
			return
		}

		// Переходим к выбору тарифов
		h.cache.SetInt(userID, promoCodeAdminFSMStateTariffs)
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: msgID,
			Text:      "Выберите <b>применимые тарифы</b>:",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "📋 Все тарифы", CallbackData: "promo_tariffs_all"}},
					{{Text: "🎯 Выбрать конкретные", CallbackData: "promo_tariffs_select"}},
					{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка перехода к выбору тарифов", "error", err)
		}
		return
	case callback.Data == "promo_tariffs_all":
		// Выбор всех тарифов
		userID := callback.From.ID

		// Проверяем состояние FSM
		state, ok := h.cache.GetInt(userID)
		if !ok || state != promoCodeAdminFSMStateTariffs {
			return
		}

		// Сохраняем выбор всех тарифов
		h.cache.SetString(8000007+userID, "all")

		// Переходим к подтверждению
		h.showPromoCodeConfirmation(ctx, b, callback)
		return

	case callback.Data == "promo_tariffs_select":
		// Выбор конкретных тарифов
		h.showTariffSelection(ctx, b, callback)
		return
	case callback.Data == "promo_create_confirm":
		// Подтверждение создания промокода
		h.createPromoCodeFromCache(ctx, b, callback)
		return
	case strings.HasPrefix(callback.Data, "promocode_delete_confirm_"):
		// Подтверждение удаления промокода
		h.handlePromoCodeDeleteConfirm(ctx, b, callback)
		return
	case strings.HasPrefix(callback.Data, "promo_tariff_toggle_"):
		// Переключение выбора тарифа
		h.handleTariffToggle(ctx, b, callback)
		return
	case callback.Data == "promo_tariffs_confirm":
		// Подтверждение выбора тарифов
		h.handleTariffsConfirm(ctx, b, callback)
		return
	case callback.Data == "buy_promo_yes":
		// Пользователь хочет использовать промокод
		h.HandleBuyPromoYes(ctx, b, update)
		return
	case callback.Data == "buy_promo_no":
		// Пользователь не хочет использовать промокод
		h.HandleBuyPromoNo(ctx, b, update)
		return
	}
}

// clearPromoCodeMessages очищает сообщения промокодов
func (h *CallbackHandler) clearPromoCodeMessages(ctx context.Context, b *bot.Bot, userID, chatID int64, exceptMsgID ...int) {
	if h.PromoCodeAdminHandler != nil {
		h.PromoCodeAdminHandler.clearPromoCodeMessages(ctx, b, userID, chatID, exceptMsgID...)
	}
}

// showPromoCodeConfirmation показывает подтверждение создания промокода
func (h *CallbackHandler) showPromoCodeConfirmation(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery) {
	userID := callback.From.ID

	// Получаем все данные из кэша
	code, _ := h.cache.GetString(8000000 + userID)
	description, _ := h.cache.GetString(8000001 + userID)
	discount, _ := h.cache.GetInt(8000002 + userID)
	expiryType, _ := h.cache.GetString(8000003 + userID)
	expiryValue, _ := h.cache.GetInt(8000004 + userID)
	activationLimit, _ := h.cache.GetInt(8000005 + userID)
	userType, _ := h.cache.GetString(8000006 + userID)
	tariffs, _ := h.cache.GetString(8000007 + userID)

	// Формируем текст подтверждения
	var expiryText string
	// Используем правильное форматирование времени как в системе рассылок
	loc, err := time.LoadLocation(config.TimeZone())
	if err != nil {
		loc = time.FixedZone("UTC+3", 3*60*60) // fallback
	}
	now := time.Now().In(loc)

	switch expiryType {
	case "hours":
		expiryTime := now.Add(time.Duration(expiryValue) * time.Hour)
		expiryText = fmt.Sprintf("до %s", FormatTimeWithTZ(expiryTime))
	case "days":
		expiryTime := now.AddDate(0, 0, expiryValue)
		expiryText = fmt.Sprintf("до %s", FormatTimeWithTZ(expiryTime))
	case "end_of_day":
		// До конца текущего дня
		endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, loc)
		expiryText = fmt.Sprintf("до %s", FormatTimeWithTZ(endOfDay))
	}

	var userTypeText string
	switch userType {
	case "with_subscription":
		userTypeText = "С активной подпиской"
	case "without_subscription":
		userTypeText = "Без активной подписки"
	case "all":
		userTypeText = "Все пользователи"
	}

	var tariffsText string
	if tariffs == "all" {
		tariffsText = "Все тарифы"
	} else if tariffs != "" {
		// Получаем названия выбранных тарифов
		selectedCodes := strings.Split(tariffs, ",")
		var tariffNames []string
		for _, code := range selectedCodes {
			tariff, err := h.tariffRepository.GetByCode(ctx, code)
			if err == nil && tariff != nil {
				tariffNames = append(tariffNames, tariff.Title)
			}
		}
		if len(tariffNames) > 0 {
			tariffsText = strings.Join(tariffNames, ", ")
		} else {
			tariffsText = "Выбранные тарифы"
		}
	} else {
		tariffsText = "Все тарифы"
	}

	var limitText string
	if activationLimit == 0 {
		limitText = "Без ограничений"
	} else {
		limitText = fmt.Sprintf("%d активаций", activationLimit)
	}

	text := fmt.Sprintf(
		"<b>Подтверждение создания промокода</b>\n\n"+
			"Код: <b>%s</b>\n"+
			"Описание: %s\n"+
			"Скидка: <b>%d%%</b>\n"+
			"Срок действия: %s\n"+
			"Лимит активаций: %s\n"+
			"Тип пользователей: %s\n"+
			"Применимые тарифы: %s\n\n"+
			"Создать промокод?",
		code, description, discount, expiryText, limitText, userTypeText, tariffsText,
	)

	keyboard := [][]models.InlineKeyboardButton{
		{{Text: "✅ Создать", CallbackData: "promo_create_confirm"}},
		{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
	}

	msgID, ok := h.cache.GetInt(9999999 + userID)
	if !ok {
		return
	}

	h.cache.SetInt(userID, promoCodeAdminFSMStateConfirm)

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Message.Message.Chat.ID,
		MessageID: msgID,
		Text:      text,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: keyboard,
		},
	})

	if err != nil {
		slog.Error("Ошибка показа подтверждения промокода", "error", err)
	}
}

// createPromoCodeFromCache создает промокод из данных в кэше
func (h *CallbackHandler) createPromoCodeFromCache(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery) {
	userID := callback.From.ID

	// Проверяем состояние FSM
	state, ok := h.cache.GetInt(userID)
	if !ok || state != promoCodeAdminFSMStateConfirm {
		return
	}

	// Получаем все данные из кэша
	code, _ := h.cache.GetString(8000000 + userID)
	description, _ := h.cache.GetString(8000001 + userID)
	discount, _ := h.cache.GetInt(8000002 + userID)
	expiryType, _ := h.cache.GetString(8000003 + userID)
	expiryValue, _ := h.cache.GetInt(8000004 + userID)
	activationLimit, _ := h.cache.GetInt(8000005 + userID)
	userType, _ := h.cache.GetString(8000006 + userID)
	tariffs, _ := h.cache.GetString(8000007 + userID)

	// Создаем промокод
	promoCode := &database.PromoCode{
		Code:            code,
		Description:     description,
		DiscountPercent: discount,
		ActivationLimit: activationLimit,
		Active:          true,
		CreatedBy:       &userID,
	}

	// Устанавливаем тип срока действия
	switch expiryType {
	case "hours":
		promoCode.ExpiryType = database.PromoCodeExpiryTypeHours
		promoCode.ExpiryValue = &expiryValue
	case "days":
		promoCode.ExpiryType = database.PromoCodeExpiryTypeDays
		promoCode.ExpiryValue = &expiryValue
	case "end_of_day":
		promoCode.ExpiryType = database.PromoCodeExpiryTypeEndOfDay
	}

	// Устанавливаем тип пользователей
	switch userType {
	case "with_subscription":
		promoCode.UserType = database.PromoCodeUserTypeWithSubscription
	case "without_subscription":
		promoCode.UserType = database.PromoCodeUserTypeWithoutSubscription
	case "all":
		promoCode.UserType = database.PromoCodeUserTypeAll
	}

	// Устанавливаем применимые тарифы
	if tariffs == "all" || tariffs == "" {
		promoCode.ApplicableTariffs = database.ApplicableTariffs{All: true}
	} else {
		// Устанавливаем конкретные тарифы
		selectedCodes := strings.Split(tariffs, ",")
		promoCode.ApplicableTariffs = database.ApplicableTariffs{
			All:   false,
			Codes: selectedCodes,
		}
	}

	// Создаем промокод через сервис
	if h.PromoCodeAdminHandler != nil && h.PromoCodeAdminHandler.promoCodeService != nil {
		_, err := h.PromoCodeAdminHandler.promoCodeService.CreatePromoCode(ctx, promoCode)
		if err != nil {
			slog.Error("Ошибка создания промокода", "error", err)

			msgID, ok := h.cache.GetInt(9999999 + userID)
			if ok {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    callback.Message.Message.Chat.ID,
					MessageID: msgID,
					Text:      fmt.Sprintf("❌ Ошибка создания промокода: %s", err.Error()),
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "⬅️ Назад", CallbackData: "admin_promocodes"}},
						},
					},
				})
			}
			return
		}

		// Успешно создан
		msgID, ok := h.cache.GetInt(9999999 + userID)
		if ok {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: msgID,
				Text:      fmt.Sprintf("✅ Промокод <b>%s</b> успешно создан!", code),
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "🎟️ К промокодам", CallbackData: "admin_promocodes"}},
					},
				},
			})
		}

		// Очищаем кэш
		h.cache.SetInt(userID, 0)
		h.cache.SetString(8000000+userID, "")
		h.cache.SetString(8000001+userID, "")
		h.cache.SetInt(8000002+userID, 0)
		h.cache.SetString(8000003+userID, "")
		h.cache.SetInt(8000004+userID, 0)
		h.cache.SetInt(8000005+userID, 0)
		h.cache.SetString(8000006+userID, "")
		h.cache.SetString(8000007+userID, "")
	}
}

// handlePromoCodeToggle обрабатывает переключение статуса промокода
func (h *CallbackHandler) handlePromoCodeToggle(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery) {
	// Извлекаем ID промокода и страницу из callback data
	data := strings.TrimPrefix(callback.Data, "promocode_toggle_")
	parts := strings.Split(data, "_p")
	if len(parts) != 2 {
		return
	}

	promoCodeID, err := strconv.ParseInt(parts[0], 10, 64)
	if err != nil {
		return
	}

	page, err := strconv.Atoi(parts[1])
	if err != nil {
		page = 1
	}

	if h.PromoCodeAdminHandler != nil && h.PromoCodeAdminHandler.promoCodeService != nil {
		// Получаем все промокоды и находим нужный
		promoCodes, err := h.PromoCodeAdminHandler.promoCodeService.GetAllPromoCodes(ctx, false)
		if err != nil {
			slog.Error("Ошибка получения промокодов", "error", err)
			return
		}

		var targetPromoCode *database.PromoCode
		for _, pc := range promoCodes {
			if pc.ID == promoCodeID {
				targetPromoCode = &pc
				break
			}
		}

		if targetPromoCode == nil {
			slog.Error("Промокод не найден", "id", promoCodeID)
			return
		}

		// Переключаем статус
		newStatus := !targetPromoCode.Active
		err = h.PromoCodeAdminHandler.promoCodeService.SetPromoCodeActive(ctx, promoCodeID, newStatus)
		if err != nil {
			slog.Error("Ошибка обновления статуса промокода", "error", err, "id", promoCodeID)
			return
		}

		// Обновляем страницу промокодов
		h.PromoCodeAdminHandler.showPromoCodesPage(ctx, b, callback, page)
	}
}

// handlePromoCodeStats обрабатывает показ статистики промокода
func (h *CallbackHandler) handlePromoCodeStats(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery) {
	// Извлекаем ID промокода и страницу из callback data
	data := strings.TrimPrefix(callback.Data, "promocode_stats_")
	parts := strings.Split(data, "_p")
	if len(parts) != 2 {
		return
	}

	promoCodeID, err := strconv.ParseInt(parts[0], 10, 64)
	if err != nil {
		return
	}

	page, err := strconv.Atoi(parts[1])
	if err != nil {
		page = 1
	}

	if h.PromoCodeAdminHandler != nil && h.PromoCodeAdminHandler.promoCodeService != nil {
		// Получаем все промокоды и находим нужный
		promoCodes, err := h.PromoCodeAdminHandler.promoCodeService.GetAllPromoCodes(ctx, false)
		if err != nil {
			slog.Error("Ошибка получения промокодов", "error", err)
			return
		}

		var promoCode *database.PromoCode
		for _, pc := range promoCodes {
			if pc.ID == promoCodeID {
				promoCode = &pc
				break
			}
		}

		if promoCode == nil {
			slog.Error("Промокод не найден", "id", promoCodeID)
			return
		}

		// Получаем статистику использования
		stats, err := h.PromoCodeAdminHandler.promoCodeService.GetPromoCodeStatistics(ctx, promoCodeID)
		if err != nil {
			slog.Error("Ошибка получения статистики промокода", "error", err, "id", promoCodeID)
			stats = map[string]interface{}{"total_usages": 0}
		}

		// Формируем текст статистики
		var status string
		if promoCode.Active {
			if promoCode.IsExpired() {
				status = "⏰ Истек"
			} else if promoCode.IsLimitReached() {
				status = "🚫 Лимит исчерпан"
			} else {
				status = "✅ Активен"
			}
		} else {
			status = "❌ Неактивен"
		}

		var limitText string
		if promoCode.ActivationLimit == 0 {
			limitText = "∞"
		} else {
			limitText = fmt.Sprintf("%d/%d", promoCode.CurrentActivations, promoCode.ActivationLimit)
		}

		// Получаем общее количество использований из статистики
		totalUsages, _ := stats["total_usages"].(int)

		text := fmt.Sprintf(
			"<b>📊 Статистика промокода %s</b>\n\n"+
				"Статус: %s\n"+
				"Использований: %s\n"+
				"Скидка: <b>%d%%</b>\n"+
				"Истекает: %s\n\n"+
				"Всего активаций: <b>%d</b>\n",
			promoCode.Code, status, limitText, promoCode.DiscountPercent,
			promoCode.ExpiresAt.Format("02.01.2006 15:04"),
			totalUsages,
		)

		// Добавляем статистику по типам пользователей, если есть
		if userTypeStats, ok := stats["user_type_stats"].(map[string]int); ok && len(userTypeStats) > 0 {
			text += "\n<b>По типам пользователей:</b>\n"
			for userType, count := range userTypeStats {
				var typeText string
				switch userType {
				case "with_subscription":
					typeText = "С подпиской"
				case "without_subscription":
					typeText = "Без подписки"
				default:
					typeText = userType
				}
				text += fmt.Sprintf("• %s: %d\n", typeText, count)
			}
		}

		if totalUsages == 0 {
			text += "\nПромокод еще не использовался."
		}

		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "⬅️ Назад", CallbackData: fmt.Sprintf("admin_promocodes_page_%d", page)}},
		}

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})

		if err != nil {
			slog.Error("Ошибка отправки статистики промокода", "error", err)
		}
	}
}

// handlePromoCodeDelete обрабатывает удаление промокода
func (h *CallbackHandler) handlePromoCodeDelete(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery) {
	// Извлекаем ID промокода и страницу из callback data
	data := strings.TrimPrefix(callback.Data, "promocode_delete_")
	parts := strings.Split(data, "_p")
	if len(parts) != 2 {
		return
	}

	promoCodeID, err := strconv.ParseInt(parts[0], 10, 64)
	if err != nil {
		return
	}

	page, err := strconv.Atoi(parts[1])
	if err != nil {
		page = 1
	}

	if h.PromoCodeAdminHandler != nil && h.PromoCodeAdminHandler.promoCodeService != nil {
		// Получаем все промокоды и находим нужный
		promoCodes, err := h.PromoCodeAdminHandler.promoCodeService.GetAllPromoCodes(ctx, false)
		if err != nil {
			slog.Error("Ошибка получения промокодов", "error", err)
			return
		}

		var promoCode *database.PromoCode
		for _, pc := range promoCodes {
			if pc.ID == promoCodeID {
				promoCode = &pc
				break
			}
		}

		if promoCode == nil {
			slog.Error("Промокод не найден", "id", promoCodeID)
			return
		}

		// Показываем подтверждение удаления
		text := fmt.Sprintf(
			"<b>⚠️ Подтверждение удаления</b>\n\n"+
				"Вы действительно хотите удалить промокод <b>%s</b>?\n\n"+
				"Описание: %s\n"+
				"Скидка: <b>%d%%</b>\n\n"+
				"<b>Внимание:</b> Это действие нельзя отменить!",
			promoCode.Code, promoCode.Description, promoCode.DiscountPercent,
		)

		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "🗑️ Да, удалить", CallbackData: fmt.Sprintf("promocode_delete_confirm_%d_p%d", promoCodeID, page)}},
			{{Text: "❌ Отмена", CallbackData: fmt.Sprintf("admin_promocodes_page_%d", page)}},
		}

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})

		if err != nil {
			slog.Error("Ошибка отправки подтверждения удаления промокода", "error", err)
		}
	}
}

// handlePromoCodeDeleteConfirm обрабатывает подтверждение удаления промокода
func (h *CallbackHandler) handlePromoCodeDeleteConfirm(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery) {
	// Извлекаем ID промокода и страницу из callback data
	data := strings.TrimPrefix(callback.Data, "promocode_delete_confirm_")
	parts := strings.Split(data, "_p")
	if len(parts) != 2 {
		return
	}

	promoCodeID, err := strconv.ParseInt(parts[0], 10, 64)
	if err != nil {
		return
	}

	page, err := strconv.Atoi(parts[1])
	if err != nil {
		page = 1
	}

	if h.PromoCodeAdminHandler != nil && h.PromoCodeAdminHandler.promoCodeService != nil {
		// Удаляем промокод
		err := h.PromoCodeAdminHandler.promoCodeService.DeletePromoCode(ctx, promoCodeID)
		if err != nil {
			slog.Error("Ошибка удаления промокода", "error", err, "id", promoCodeID)

			// Показываем ошибку
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      fmt.Sprintf("❌ Ошибка удаления промокода: %s", err.Error()),
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад", CallbackData: fmt.Sprintf("admin_promocodes_page_%d", page)}},
					},
				},
			})
			return
		}

		// Очищаем предыдущие сообщения и показываем обновленную страницу промокодов
		h.clearPromoCodeMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)

		// Показываем обновленную страницу промокодов
		h.PromoCodeAdminHandler.showPromoCodesPage(ctx, b, callback, page)

		slog.Info("Промокод успешно удален", "id", promoCodeID)
	}
}

// showTariffSelection показывает интерфейс выбора конкретных тарифов
func (h *CallbackHandler) showTariffSelection(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery) {
	userID := callback.From.ID

	// Проверяем состояние FSM
	state, ok := h.cache.GetInt(userID)
	if !ok || state != promoCodeAdminFSMStateTariffs {
		return
	}

	// Получаем все тарифы
	tariffs, err := h.tariffRepository.GetAll(ctx, false)
	if err != nil {
		slog.Error("Ошибка получения тарифов", "error", err)
		return
	}

	if len(tariffs) == 0 {
		// Если нет тарифов, используем все тарифы
		h.cache.SetString(8000007+userID, "all")
		h.showPromoCodeConfirmation(ctx, b, callback)
		return
	}

	// Формируем клавиатуру с тарифами
	var keyboard [][]models.InlineKeyboardButton

	// Получаем уже выбранные тарифы из кэша (если есть)
	selectedTariffs, _ := h.cache.GetString(8000007 + userID)
	var selected []string
	if selectedTariffs != "" && selectedTariffs != "all" {
		// Парсим выбранные тарифы (формат: "code1,code2,code3")
		selected = strings.Split(selectedTariffs, ",")
	}

	// Добавляем кнопки для каждого тарифа
	for _, tariff := range tariffs {
		isSelected := false
		for _, sel := range selected {
			if sel == tariff.Code {
				isSelected = true
				break
			}
		}

		var buttonText string
		if isSelected {
			buttonText = fmt.Sprintf("✅ %s", tariff.Title)
		} else {
			buttonText = fmt.Sprintf("⬜ %s", tariff.Title)
		}

		keyboard = append(keyboard, []models.InlineKeyboardButton{
			{Text: buttonText, CallbackData: fmt.Sprintf("promo_tariff_toggle_%s", tariff.Code)},
		})
	}

	// Добавляем кнопки управления
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{Text: "✅ Подтвердить выбор", CallbackData: "promo_tariffs_confirm"},
	})
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{Text: "📋 Выбрать все", CallbackData: "promo_tariffs_all"},
		{Text: "❌ Отмена", CallbackData: "admin_promocodes"},
	})

	msgID, ok := h.cache.GetInt(9999999 + userID)
	if !ok {
		return
	}

	text := fmt.Sprintf("Выберите <b>конкретные тарифы</b> для промокода:\n\nВыбрано: <b>%d</b> из <b>%d</b>", len(selected), len(tariffs))

	// Проверяем, изменился ли текст или клавиатура
	currentText := fmt.Sprintf("tariff_selection_%d_%s", len(selected), selectedTariffs)
	lastText, _ := h.cache.GetString(9999998 + userID)

	if currentText != lastText {
		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: msgID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})

		if err != nil {
			// Игнорируем ошибку "message is not modified"
			if !strings.Contains(err.Error(), "message is not modified") {
				slog.Error("Ошибка показа выбора тарифов", "error", err)
			}
		} else {
			// Сохраняем текущее состояние для предотвращения дублирующих обновлений
			h.cache.SetString(9999998+userID, currentText)
		}
	}
}

// handleTariffToggle обрабатывает переключение выбора тарифа
func (h *CallbackHandler) handleTariffToggle(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery) {
	userID := callback.From.ID

	// Проверяем состояние FSM
	state, ok := h.cache.GetInt(userID)
	if !ok || state != promoCodeAdminFSMStateTariffs {
		return
	}

	// Извлекаем код тарифа
	tariffCode := strings.TrimPrefix(callback.Data, "promo_tariff_toggle_")

	// Получаем текущий выбор
	selectedTariffs, _ := h.cache.GetString(8000007 + userID)
	var selected []string
	if selectedTariffs != "" && selectedTariffs != "all" {
		selected = strings.Split(selectedTariffs, ",")
	}

	// Переключаем выбор
	found := false
	for i, sel := range selected {
		if sel == tariffCode {
			// Убираем из выбора
			selected = append(selected[:i], selected[i+1:]...)
			found = true
			break
		}
	}

	if !found {
		// Добавляем в выбор
		selected = append(selected, tariffCode)
	}

	// Сохраняем новый выбор
	if len(selected) == 0 {
		h.cache.SetString(8000007+userID, "")
	} else {
		h.cache.SetString(8000007+userID, strings.Join(selected, ","))
	}

	// Обновляем интерфейс
	h.showTariffSelection(ctx, b, callback)
}

// handleTariffsConfirm обрабатывает подтверждение выбора тарифов
func (h *CallbackHandler) handleTariffsConfirm(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery) {
	userID := callback.From.ID

	// Проверяем состояние FSM
	state, ok := h.cache.GetInt(userID)
	if !ok || state != promoCodeAdminFSMStateTariffs {
		return
	}

	// Проверяем, что выбран хотя бы один тариф
	selectedTariffs, _ := h.cache.GetString(8000007 + userID)
	if selectedTariffs == "" {
		// Если ничего не выбрано, показываем ошибку
		msgID, ok := h.cache.GetInt(9999999 + userID)
		if ok {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: msgID,
				Text:      "❌ Выберите хотя бы один тариф или используйте \"Все тарифы\"",
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад к выбору", CallbackData: "promo_tariffs_select"}},
						{{Text: "📋 Все тарифы", CallbackData: "promo_tariffs_all"}},
						{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
					},
				},
			})
		}
		return
	}

	// Переходим к подтверждению
	h.showPromoCodeConfirmation(ctx, b, callback)
}

// HandleBuyPromoYes обрабатывает выбор "Да" для промокода при покупке
func (h *CallbackHandler) HandleBuyPromoYes(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery
	userID := callback.From.ID

	// Устанавливаем FSM состояние для ввода промокода
	h.cache.SetInt(userID, promoCodeFSMStateInput)

	// Сохраняем ID сообщения для последующего редактирования
	h.cache.SetInt(9999999+userID, callback.Message.Message.ID)

	// Показываем сообщение для ввода промокода
	text := "🎟️ Введите ваш промокод:"
	keyboard := [][]models.InlineKeyboardButton{
		{{Text: "❌ Отмена", CallbackData: CallbackBuy}},
	}

	_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Message.Message.Chat.ID,
		MessageID: callback.Message.Message.ID,
		Text:      text,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: keyboard,
		},
	})

	if err != nil {
		slog.Error("Ошибка показа ввода промокода", "error", err)
	}
}

// HandleBuyPromoNo обрабатывает выбор "Нет" для промокода при покупке
func (h *CallbackHandler) HandleBuyPromoNo(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery
	// Показываем тарифы без промокода
	h.showTariffsForPurchase(ctx, b, callback, nil)
}

// showTariffsForPurchase показывает тарифы для покупки (с промокодом или без)
func (h *CallbackHandler) showTariffsForPurchase(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery, promoCode *string) {
	langCode := callback.From.LanguageCode

	// Получаем тарифы из БД
	tariffs, err := h.tariffRepository.GetAll(ctx, true)
	if err != nil {
		slog.Error("Ошибка получения тарифов", "error", err)
		return
	}

	if len(tariffs) == 0 {
		_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
			Text:      "❌ Нет доступных тарифов",
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackStart}},
				},
			},
		})
		return
	}

	var priceButtons []models.InlineKeyboardButton
	var discountPercent int

	// Если есть промокод, получаем процент скидки
	if promoCode != nil && h.PromoCodeAdminHandler != nil {
		validPromoCode, err := h.PromoCodeAdminHandler.promoCodeService.GetPromoCodeByCode(ctx, *promoCode)
		if err == nil && validPromoCode != nil {
			discountPercent = validPromoCode.DiscountPercent
		}
	}

	// Формируем кнопки тарифов
	for _, tariff := range tariffs {
		var buttonText string

		if promoCode != nil && discountPercent > 0 && h.PromoCodeAdminHandler != nil {
			// Рассчитываем цены со скидкой
			_, discountedPriceRUB := h.PromoCodeAdminHandler.promoCodeService.CalculateDiscountedPrice(tariff.PriceRUB, discountPercent, "RUB")
			_, discountedPriceStars := h.PromoCodeAdminHandler.promoCodeService.CalculateDiscountedPrice(tariff.PriceStars, discountPercent, "STARS")

			// Сокращенный текст кнопки с промокодом
			buttonText = fmt.Sprintf("%s -%d%% 🎉 (%d₽/%d⭐)",
				tariff.Title, discountPercent, discountedPriceRUB, discountedPriceStars)
		} else {
			// Сокращенный текст кнопки без промокода
			buttonText = fmt.Sprintf("%s (%d₽/%d⭐)", tariff.Title, tariff.PriceRUB, tariff.PriceStars)
		}

		callbackData := fmt.Sprintf("%s?code=%s", CallbackSell, tariff.Code)
		if promoCode != nil {
			callbackData += "&promo=" + *promoCode
		}

		btn := models.InlineKeyboardButton{
			Text:         buttonText,
			CallbackData: callbackData,
		}
		priceButtons = append(priceButtons, btn)
	}

	// Формируем клавиатуру
	keyboard := [][]models.InlineKeyboardButton{}

	if len(priceButtons) == 4 {
		keyboard = append(keyboard, priceButtons[:2])
		keyboard = append(keyboard, priceButtons[2:])
	} else if len(priceButtons) > 0 {
		keyboard = append(keyboard, priceButtons)
	}

	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackBuy},
	})

	// Формируем текст сообщения
	var text string
	if promoCode != nil {
		text = fmt.Sprintf("✅ Промокод применен! Скидка: <b>%d%%</b>\n\n%s",
			discountPercent, h.translation.GetText(langCode, "pricing_info"))
	} else {
		text = h.translation.GetText(langCode, "pricing_info")
	}

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Message.Message.Chat.ID,
		MessageID: callback.Message.Message.ID,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: keyboard,
		},
		Text: text,
	})

	if err != nil {
		slog.Error("Ошибка показа тарифов", "error", err)
	}
}
