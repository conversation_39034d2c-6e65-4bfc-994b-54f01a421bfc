package handler

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/database"

	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

const (
	adminBroadcastStateWaitText    = 1
	adminBroadcastStateCreateText  = 1001
	adminBroadcastStateCreateTime  = 1002
	adminBroadcastStateCreateReady = 1003

	adminBroadcastStateInstantText         = 10
	adminBroadcastStateInstantConfirm      = 11
	adminBroadcastStateInstantTargetSelect = 12

	adminBroadcastStateEditText  = 3001
	adminBroadcastStateEditTime  = 3002
	adminBroadcastStateEditReady = 3003
)

type BroadcastHandler struct {
	*AdminHandler
}

func NewBroadcastHandler(adminHandler *AdminHandler) *BroadcastHandler {
	return &BroadcastHandler{AdminHandler: adminHandler}
}

func (h *BroadcastHandler) AdminBroadcastTextHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	if update == nil || update.Message == nil {
		slog.Error("[AdminBroadcastTextHandler] update or message is nil", "update", update)
		return
	}
	userID := update.Message.From.ID
	state, ok := h.cache.GetInt(userID)
	if !ok {
		slog.Warn("[AdminBroadcastTextHandler] FSM state not found", "user_id", userID)
		return
	}
	slog.Info("[AdminBroadcastTextHandler] вход", "user_id", userID, "state", state, "text", update.Message.Text)
	if state == adminBroadcastStateInstantText {
		text := update.Message.Text
		h.cache.SetString(9000000+userID, text)
		h.cache.SetInt(userID, adminBroadcastStateInstantTargetSelect)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: update.Message.ID,
		})
		msgID, ok := h.cache.GetInt(9999999 + userID)
		if !ok {
			msgReply, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID: update.Message.Chat.ID,
				Text:   fmt.Sprintf("Текст рассылки:\n%s\n\nВыберите аудиторию для рассылки:", text),
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "🗣️ Всем пользователям", CallbackData: "instant_broadcast_send_all"}},
						{{Text: "👤 Без подписки", CallbackData: "instant_broadcast_send_unsubscribed"}},
						{{Text: "❌ Отмена", CallbackData: "instant_broadcast_cancel"}},
					},
				},
			})
			if err != nil {
				slog.Error("Ошибка отправки сообщения для подтверждения моментальной рассылки", err)
				return
			}
			if msgReply != nil {
				h.cache.SetInt(9999999+userID, msgReply.ID)
			}
			return
		}
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: msgID,
			Text:      fmt.Sprintf("Текст рассылки:\n%s\n\nВыберите аудиторию для рассылки:", text),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "🗣️ Всем пользователям", CallbackData: "instant_broadcast_send_all"}},
					{{Text: "👤 Без подписки", CallbackData: "instant_broadcast_send_unsubscribed"}},
					{{Text: "❌ Отмена", CallbackData: "instant_broadcast_cancel"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка редактирования сообщения для подтверждения моментальной рассылки", err)
		} else if msgEdit != nil {
			h.cache.SetInt(9999999+userID, msgEdit.ID)
		}
		return
	}
	if state == adminBroadcastStateEditText {
		text := update.Message.Text
		h.cache.SetString(3000000+userID, text)
		msgID, ok := h.cache.GetInt(9999999 + userID)
		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "❌ Отмена", CallbackData: "broadcast_list"}},
		}
		oldTime, _ := h.cache.GetString(4000000 + userID)
		editText := "Текст сохранён. Теперь введите новую дату и время рассылки в формате ДД.ММ.ГГГГ ЧЧ:ММ (например, 31.12.2025 23:59).\nТекущее время: " + FormatTimeWithTZMustParse(oldTime) + "\nЕсли хотите оставить текущее время, просто повторите его."
		if ok {
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: msgID,
				Text:      editText,
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: keyboard,
				},
			})
			if err != nil {
				slog.Error("Ошибка редактирования сообщения для ввода времени при редактировании", err)
			} else {
				h.cache.SetInt(9999999+userID, msgEdit.ID)
			}
		} else {
			msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID:    update.Message.Chat.ID,
				Text:      editText,
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: keyboard,
				},
			})
			if err != nil {
				slog.Error("Ошибка отправки нового сообщения для ввода времени при редактировании", err)
			} else if msg != nil {
				h.cache.SetInt(9999999+userID, msg.ID)
			}
			if update.CallbackQuery != nil && update.CallbackQuery.Message.Message != nil {
				_, errDel := b.DeleteMessage(ctx, &bot.DeleteMessageParams{
					ChatID:    update.CallbackQuery.Message.Message.Chat.ID,
					MessageID: update.CallbackQuery.Message.Message.ID,
				})
				if errDel != nil {
					slog.Warn("Не удалось удалить старое меню при переходе к этапу времени", "err", errDel)
				}
			}
		}
		h.cache.SetInt(userID, adminBroadcastStateEditTime)
		_, errDelUser := b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: update.Message.ID,
		})
		if errDelUser != nil {
			slog.Warn("Не удалось удалить сообщение пользователя с текстом при редактировании", "err", errDelUser)
		}
		return
	}
	if state == adminBroadcastStateEditTime {
		idInt, _ := h.cache.GetInt(7000000 + userID)
		id := int64(idInt)
		input := update.Message.Text
		textMsg, _ := h.cache.GetString(3000000 + userID)
		if input == textMsg {
			b.DeleteMessage(ctx, &bot.DeleteMessageParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: update.Message.ID,
			})
			msgID, ok := h.cache.GetInt(9999999 + userID)
			if !ok {
				return
			}
			text := "Введённое время совпадает с текстом рассылки. Пожалуйста, введите дату и время, отличные от текста рассылки (ДД.ММ.ГГГГ ЧЧ:ММ):"
			keyboard := [][]models.InlineKeyboardButton{
				{{Text: "❌ Отмена", CallbackData: "broadcast_list"}},
			}
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: msgID,
				Text:      text,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: keyboard,
				},
			})
			if err != nil {
				slog.Error("Ошибка редактирования сообщения для повторного ввода времени при совпадении с текстом", err)
			} else {
				h.cache.SetInt(9999999+userID, msgEdit.ID)
			}
			return
		}
		loc, _ := time.LoadLocation(config.TimeZone())
		now := time.Now().In(loc)
		t, err := time.ParseInLocation("02.01.2006 15:04", input, loc)
		if err != nil || !t.After(now) || t.After(now.Add(48*time.Hour)) {
			b.DeleteMessage(ctx, &bot.DeleteMessageParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: update.Message.ID,
			})
			msgID, ok := h.cache.GetInt(9999999 + userID)
			if !ok {
				return
			}
			var errorText string
			if err != nil {
				errorText = "Неверный формат даты/времени."
			} else if !t.After(now) {
				errorText = "Рассылка должна быть не ранее текущей даты."
			} else {
				errorText = "Рассылка должна быть не позднее 48 часов от текущей даты."
			}
			text := fmt.Sprintf("%s\n\nПопробуйте ещё раз (ДД.ММ.ГГГГ ЧЧ:ММ):", errorText)
			keyboard := [][]models.InlineKeyboardButton{
				{{Text: "❌ Отмена", CallbackData: "broadcast_list"}},
			}
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: msgID,
				Text:      text,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: keyboard,
				},
			})
			if err != nil {
				slog.Error("Ошибка редактирования сообщения для повторного ввода времени при редактировании", err)
			} else {
				h.cache.SetInt(9999999+userID, msgEdit.ID)
			}
			return
		}
		h.cache.SetString(4000000+userID, t.Format(time.RFC3339))
		msgID, ok := h.cache.GetInt(9999999 + userID)
		if !ok {
			return
		}
		text, _ := h.cache.GetString(3000000 + userID)
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: msgID,
			Text:      fmt.Sprintf("Сохранить изменения?\nНовый текст: %s\nНовое время: %s", text, FormatTimeWithTZ(t)),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "💾 Сохранить", CallbackData: fmt.Sprintf("broadcast_edit_confirm_%d", id)}},
					{{Text: "❌ Отмена", CallbackData: "broadcast_list"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка редактирования сообщения для подтверждения редактирования рассылки", err)
		} else {
			h.cache.SetInt(9999999+userID, msgEdit.ID)
		}
		h.cache.SetInt(userID, adminBroadcastStateEditReady)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: update.Message.ID,
		})
		return
	}
	if state == adminBroadcastStateInstantConfirm {
		return
	}
	switch state {
	case adminBroadcastStateCreateText:
		text := update.Message.Text
		h.cache.SetString(1000000+userID, text)
		h.cache.SetInt(userID, adminBroadcastStateCreateTime)

		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: update.Message.ID,
		})

		msgID, ok := h.cache.GetInt(9999999 + userID)
		if !ok {
			return
		}

		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "❌ Отмена", CallbackData: "broadcast_create_cancel"}},
		}
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: msgID,
			Text:      "Введите дату и время рассылки в формате ДД.ММ.ГГГГ ЧЧ:ММ (например, 31.12.2025 23:59). Время указывается по " + formatCurrentTZLabel() + ":",
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка редактирования сообщения для ввода времени рассылки", err)
		} else if msgEdit != nil {
			h.cache.SetInt(9999999+userID, msgEdit.ID)
		}
		return
	case adminBroadcastStateCreateTime:
		input := update.Message.Text
		loc, _ := time.LoadLocation(config.TimeZone())
		now := time.Now().In(loc)
		t, err := time.ParseInLocation("02.01.2006 15:04", input, loc)
		if err != nil || !t.After(now) || t.After(now.Add(48*time.Hour)) {
			b.DeleteMessage(ctx, &bot.DeleteMessageParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: update.Message.ID,
			})

			msgID, ok := h.cache.GetInt(9999999 + userID)
			if !ok {
				slog.Error("Не удалось получить ID сообщения для редактирования")
				return
			}

			var errorText string
			if err != nil {
				errorText = "Неверный формат даты/времени."
			} else if !t.After(now) {
				errorText = "Рассылка должна быть не ранее текущий даты."
			} else {
				errorText = "Рассылка должна быть не позднее 48 часов от текущей даты."
			}

			text := fmt.Sprintf("%s\n\nПопробуйте ещё раз (ДД.ММ.ГГГГ ЧЧ:ММ):", errorText)
			keyboard := [][]models.InlineKeyboardButton{
				{{Text: "❌ Отмена", CallbackData: "broadcast_create_cancel"}},
			}

			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: msgID,
				Text:      text,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: keyboard,
				},
			})
			if err != nil {
				slog.Error("Ошибка редактирования сообщения для повторного ввода времени", "err", err)
			} else {
				h.cache.SetInt(9999999+userID, msgEdit.ID)
			}
			return
		}

		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: update.Message.ID,
		})

		msgID, ok := h.cache.GetInt(9999999 + userID)
		if !ok {
			slog.Error("Не удалось получить ID сообщения для редактирования")
			return
		}

		h.cache.SetString(2000000+userID, t.Format(time.RFC3339))
		text, _ := h.cache.GetString(1000000 + userID)
		msgEditText := fmt.Sprintf("Создать рассылку?\nТекст: %s\nВремя: %s\n\nВыберите аудиторию:", text, FormatTimeWithTZ(t))
		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "🗣️ Всем пользователям", CallbackData: "broadcast_create_confirm_all"}},
			{{Text: "👤 Без подписки", CallbackData: "broadcast_create_confirm_unsubscribed"}},
			{{Text: "❌ Отмена", CallbackData: "broadcast_create_cancel"}},
		}
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: msgID,
			Text:      msgEditText,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка редактирования сообщения для подтверждения создания рассылки", err)
		} else {
			h.cache.SetInt(9999999+userID, msgEdit.ID)
		}
		h.cache.SetInt(userID, adminBroadcastStateCreateReady)
		return
	case adminBroadcastStateCreateReady:
		return
	}
}

const broadcastsPerPage = 3

func (h *BroadcastHandler) clearBroadcastMessages(ctx context.Context, b *bot.Bot, userID, chatID int64, exceptMsgID ...int) {
	if oldMsgIDs, ok := h.cache.Get(5000000 + userID); ok {
		if ids, ok := oldMsgIDs.([]int); ok {
			for _, id := range ids {
				skip := false
				for _, except := range exceptMsgID {
					if id == except {
						skip = true
						break
					}
				}
				if skip {
					continue
				}
				b.DeleteMessage(ctx, &bot.DeleteMessageParams{
					ChatID:    chatID,
					MessageID: id,
				})
			}
		}
		h.cache.Delete(5000000 + userID)
	}
}

func getBroadcastPage(tasks []database.BroadcastTask, page int) ([]database.BroadcastTask, int) {
	if page < 1 {
		page = 1
	}
	total := len(tasks)
	start := (page - 1) * broadcastsPerPage
	if start >= total {
		return nil, (total + broadcastsPerPage - 1) / broadcastsPerPage
	}
	end := start + broadcastsPerPage
	if end > total {
		end = total
	}
	return tasks[start:end], (total + broadcastsPerPage - 1) / broadcastsPerPage
}

func buildBroadcastMenu() (string, [][]models.InlineKeyboardButton) {
	return "<b>Меню рассылок:</b>", [][]models.InlineKeyboardButton{
		{{Text: "⚡ Моментальная рассылка", CallbackData: "instant_broadcast"}},
		{{Text: "🕒 Отложенные рассылки", CallbackData: "broadcast_list"}},
		{{Text: "⬅️ Назад", CallbackData: "admin_menu"}},
	}
}

func buildInstantBroadcastMenu() (string, [][]models.InlineKeyboardButton) {
	return "<b>Моментальная рассылка:</b>\nВведите текст рассылки (будет отправлен сразу после подтверждения):", [][]models.InlineKeyboardButton{
		{{Text: "❌ Отмена", CallbackData: "admin_broadcast"}},
	}
}

func buildCreateBroadcastMenu() (string, [][]models.InlineKeyboardButton) {
	return "<b>Создание новой рассылки:</b>\nВведите текст рассылки:", [][]models.InlineKeyboardButton{
		{{Text: "❌ Отмена", CallbackData: "broadcast_create_cancel"}},
	}
}

func buildEditBroadcastMenu(task *database.BroadcastTask, page int) (string, [][]models.InlineKeyboardButton) {
	text := fmt.Sprintf("<b>Редактирование рассылки:</b>\nТекущий текст: %s\nВведите новый текст рассылки (можно ввести любой текст, даже если он похож на дату/время):", task.Message)
	return text, [][]models.InlineKeyboardButton{
		{{Text: "❌ Отмена", CallbackData: "broadcast_list"}},
	}
}

func (h *BroadcastHandler) sendPaginatedBroadcastList(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery, page int) {
	userID := callback.From.ID
	b.DeleteMessage(ctx, &bot.DeleteMessageParams{
		ChatID:    callback.Message.Message.Chat.ID,
		MessageID: callback.Message.Message.ID,
	})
	tasks, err := h.broadcastTaskService.GetAll(ctx)
	if err != nil {
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID: callback.Message.Message.Chat.ID,
			Text:   "Ошибка получения списка рассылок.",
		})
		if err == nil {
			h.cache.SetInt(9999999+userID, msg.ID)
		}
		return
	}

	pagedTasks, totalPages := getBroadcastPage(tasks, page)
	if totalPages > 0 && page > totalPages {
		page = totalPages
		pagedTasks, totalPages = getBroadcastPage(tasks, page)
	}

	var sentMessages []int

	if len(pagedTasks) == 0 {
		text := "Нет запланированных рассылок."
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
		})
		if err == nil {
			sentMessages = append(sentMessages, msg.ID)
		}
	} else {
		for _, task := range pagedTasks {
			text, keyboard := buildSingleBroadcastEntry(task, page)
			msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID:      callback.Message.Message.Chat.ID,
				Text:        text,
				ParseMode:   models.ParseModeHTML,
				ReplyMarkup: keyboard,
			})
			if err == nil {
				sentMessages = append(sentMessages, msg.ID)
			}
		}
	}

	var paginationKeyboard []models.InlineKeyboardButton
	if page > 1 {
		paginationKeyboard = append(paginationKeyboard, models.InlineKeyboardButton{Text: "◀️ Предыдущая", CallbackData: fmt.Sprintf("broadcast_list_page_%d", page-1)})
	}
	paginationKeyboard = append(paginationKeyboard, models.InlineKeyboardButton{Text: "➕ Создать", CallbackData: "broadcast_create"})
	if page < totalPages {
		paginationKeyboard = append(paginationKeyboard, models.InlineKeyboardButton{Text: "▶️ Следующая", CallbackData: fmt.Sprintf("broadcast_list_page_%d", page+1)})
	}

	navKeyboard := [][]models.InlineKeyboardButton{paginationKeyboard, {{Text: "⬅️ Назад", CallbackData: "admin_broadcast"}}}
	msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID: callback.Message.Message.Chat.ID,
		Text:   fmt.Sprintf("Страница %d из %d", page, totalPages),
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: navKeyboard,
		},
	})
	if err == nil {
		sentMessages = append(sentMessages, msg.ID)
	}

	h.cache.Set(5000000+userID, sentMessages)
}

func buildSingleBroadcastEntry(task database.BroadcastTask, page int) (string, models.InlineKeyboardMarkup) {
	var status string
	switch task.Status {
	case database.BroadcastTaskStatusPending:
		status = "⏳ Ожидает"
	case database.BroadcastTaskStatusSent:
		status = "✅ Отправлено"
	case database.BroadcastTaskStatusCancelled:
		status = "❌ Отменено"
	}

	text := fmt.Sprintf(
		"<b>Рассылка #%d</b>\n\nТекст: %s\nСтатус: %s\nАудитория: %s\nЗапланировано на: %s",
		task.ID, task.Message, status, TranslateAudience(task.TargetAudience), FormatTimeWithTZ(task.SendAt),
	)

	var keyboard [][]models.InlineKeyboardButton

	// Формируем кнопки в зависимости от статуса рассылки
	switch task.Status {
	case database.BroadcastTaskStatusPending:
		// Для ожидающих рассылок: запуск, редактирование и удаление
		keyboard = [][]models.InlineKeyboardButton{
			{
				{Text: "🚀 Запустить сейчас", CallbackData: fmt.Sprintf("broadcast_run_now_%d_p%d", task.ID, page)},
			},
			{
				{Text: "✏️ Редактировать", CallbackData: fmt.Sprintf("broadcast_edit_%d_p%d", task.ID, page)},
				{Text: "🗑 Удалить", CallbackData: fmt.Sprintf("broadcast_delete_%d_p%d", task.ID, page)},
			},
		}
	case database.BroadcastTaskStatusSent:
		// Для отправленных рассылок: только удаление
		keyboard = [][]models.InlineKeyboardButton{
			{
				{Text: "🗑 Удалить", CallbackData: fmt.Sprintf("broadcast_delete_%d_p%d", task.ID, page)},
			},
		}
	default:
		// Для отмененных и других статусов: редактирование и удаление
		keyboard = [][]models.InlineKeyboardButton{
			{
				{Text: "✏️ Редактировать", CallbackData: fmt.Sprintf("broadcast_edit_%d_p%d", task.ID, page)},
				{Text: "🗑 Удалить", CallbackData: fmt.Sprintf("broadcast_delete_%d_p%d", task.ID, page)},
			},
		}
	}

	return text, models.InlineKeyboardMarkup{InlineKeyboard: keyboard}
}

func buildSuccessBroadcastCreatedMenu(targetAudience string) (string, [][]models.InlineKeyboardButton) {
	text := fmt.Sprintf("✅ Рассылка для аудитории <b>%s</b> успешно создана и запланирована!", TranslateAudience(targetAudience))
	keyboard := [][]models.InlineKeyboardButton{
		{{Text: "⬅️ В меню рассылок", CallbackData: "admin_broadcast"}},
	}
	return text, keyboard
}

func TranslateAudience(audience string) string {
	switch audience {
	case "all":
		return "Всем пользователям"
	case "unsubscribed":
		return "Без подписки"
	default:
		return audience
	}
}
