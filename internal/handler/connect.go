package handler

import (
	"context"
	"fmt"
	"strings"
	"time"

	"log/slog"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"

	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/translation"
	"remnawave-tg-shop-bot/utils"
)

func (h Handler) ConnectCommandHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	customer, err := h.customerRepository.FindByTelegramId(ctx, update.Message.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", err)
		return
	}
	if customer == nil {
		slog.Error("customer not exist", "telegramId", utils.MaskHalfInt64(update.Message.Chat.ID), "error", err)
		return
	}

	langCode := update.Message.From.LanguageCode

	// Получаем настройки автопродления
	autoRenewalSettings, err := h.autoRenewalService.GetAutoRenewalSettings(ctx, customer.ID)
	if err != nil {
		// Если настроек нет, это нормально - значит автопродление не настроено
		autoRenewalSettings = nil
	}

	isDisabled := true
	_, err = b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:    update.Message.Chat.ID,
		Text:      buildConnectTextWithAutoRenewal(customer, autoRenewalSettings, langCode),
		ParseMode: models.ParseModeHTML,
		LinkPreviewOptions: &models.LinkPreviewOptions{
			IsDisabled: &isDisabled,
		},
		ReplyMarkup: buildConnectKeyboard(customer, autoRenewalSettings, langCode, h.translation),
	})

	if err != nil {
		slog.Error("Error sending connect message", err)
	}
}

func (h Handler) ConnectCallbackHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", err)
		return
	}
	if customer == nil {
		slog.Error("customer not exist", "telegramId", utils.MaskHalfInt64(callback.Chat.ID), "error", err)
		return
	}

	langCode := update.CallbackQuery.From.LanguageCode

	// Получаем настройки автопродления
	autoRenewalSettings, err := h.autoRenewalService.GetAutoRenewalSettings(ctx, customer.ID)
	if err != nil {
		// Если настроек нет, это нормально - значит автопродление не настроено
		autoRenewalSettings = nil
	}

	isDisabled := true
	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		ParseMode: models.ParseModeHTML,
		Text:      buildConnectTextWithAutoRenewal(customer, autoRenewalSettings, langCode),
		LinkPreviewOptions: &models.LinkPreviewOptions{
			IsDisabled: &isDisabled,
		},
		ReplyMarkup: buildConnectKeyboard(customer, autoRenewalSettings, langCode, h.translation),
	})

	if err != nil {
		slog.Error("Error sending connect message", err)
	}
}

func buildConnectText(customer *database.Customer, langCode string) string {
	var info strings.Builder

	tm := translation.GetInstance()

	if customer.ExpireAt != nil {
		currentTime := time.Now()

		if currentTime.Before(*customer.ExpireAt) {
			formattedDate := customer.ExpireAt.Format("02.01.2006 15:04")

			subscriptionActiveText := tm.GetText(langCode, "subscription_active")
			info.WriteString(fmt.Sprintf(subscriptionActiveText, formattedDate))

			if customer.SubscriptionLink != nil && *customer.SubscriptionLink != "" {
				subscriptionLinkText := tm.GetText(langCode, "subscription_link")
				info.WriteString(fmt.Sprintf(subscriptionLinkText, *customer.SubscriptionLink))
			}

			// TODO: Добавить информацию об автопродлении
			// Здесь должна быть интеграция с AutoRenewalService для получения настроек
			// и отображения статуса автопродления
		} else {
			noSubscriptionText := tm.GetText(langCode, "no_subscription")
			info.WriteString(noSubscriptionText)
		}
	} else {
		noSubscriptionText := tm.GetText(langCode, "no_subscription")
		info.WriteString(noSubscriptionText)
	}

	return info.String()
}

// buildConnectTextWithAutoRenewal - расширенная версия buildConnectText с информацией об автопродлении
func buildConnectTextWithAutoRenewal(customer *database.Customer, autoRenewalSettings *database.AutoRenewalSettings, langCode string) string {
	var info strings.Builder

	tm := translation.GetInstance()

	if customer.ExpireAt != nil {
		currentTime := time.Now()

		if currentTime.Before(*customer.ExpireAt) {
			formattedDate := FormatTimeWithTZ(*customer.ExpireAt)

			subscriptionActiveText := tm.GetText(langCode, "subscription_active")
			info.WriteString(fmt.Sprintf(subscriptionActiveText, formattedDate))

			if customer.SubscriptionLink != nil && *customer.SubscriptionLink != "" {
				subscriptionLinkText := tm.GetText(langCode, "subscription_link")
				info.WriteString(fmt.Sprintf(subscriptionLinkText, *customer.SubscriptionLink))
			}

			// Информация об автопродлении
			info.WriteString("\n\n")
			if autoRenewalSettings != nil && autoRenewalSettings.Enabled {
				info.WriteString("🔄 ")
				info.WriteString(tm.GetText(langCode, "auto_renewal_enabled_message"))
				info.WriteString("\n")

				paymentMethodName := getPaymentMethodName(autoRenewalSettings.PaymentMethod, langCode, tm)
				info.WriteString(fmt.Sprintf(
					tm.GetText(langCode, "auto_renewal_payment_method_info"),
					paymentMethodName,
				))
			} else {
				info.WriteString("❌ ")
				info.WriteString(tm.GetText(langCode, "auto_renewal_disabled"))
			}
		} else {
			noSubscriptionText := tm.GetText(langCode, "no_subscription")
			info.WriteString(noSubscriptionText)
		}
	} else {
		noSubscriptionText := tm.GetText(langCode, "no_subscription")
		info.WriteString(noSubscriptionText)
	}

	return info.String()
}

// getPaymentMethodName - возвращает локализованное название способа оплаты
func getPaymentMethodName(paymentMethod, langCode string, tm *translation.Manager) string {
	switch paymentMethod {
	case "yookasa":
		return tm.GetText(langCode, "payment_method_card")
	case "cryptopay":
		return tm.GetText(langCode, "payment_method_crypto")
	case "telegram":
		return tm.GetText(langCode, "payment_method_stars")
	case "tribute":
		return tm.GetText(langCode, "payment_method_tribute")
	default:
		return paymentMethod
	}
}

// buildConnectKeyboard - создает клавиатуру для команды /connect с кнопками управления автопродлением
func buildConnectKeyboard(customer *database.Customer, autoRenewalSettings *database.AutoRenewalSettings, langCode string, tm *translation.Manager) models.InlineKeyboardMarkup {
	var keyboard [][]models.InlineKeyboardButton

	// Если подписка активна, добавляем кнопку "Подключиться"
	if customer.ExpireAt != nil && time.Now().Before(*customer.ExpireAt) {
		// Добавляем кнопку "Подключиться" как в /start
		connectButton := resolveConnectButton(customer, langCode, tm)
		if len(connectButton) > 0 {
			keyboard = append(keyboard, connectButton)
		}

		// Показываем кнопки управления автопродлением
		if autoRenewalSettings != nil && autoRenewalSettings.Enabled {
			// Автопродление включено - показываем кнопки управления
			keyboard = append(keyboard, []models.InlineKeyboardButton{
				{
					Text:         tm.GetText(langCode, "change_payment_method_button"),
					CallbackData: "auto_renewal_change_payment",
				},
			})
			keyboard = append(keyboard, []models.InlineKeyboardButton{
				{
					Text:         tm.GetText(langCode, "disable_auto_renewal_button"),
					CallbackData: "auto_renewal_disable",
				},
			})
		} else {
			// Автопродление выключено - показываем кнопку включения
			keyboard = append(keyboard, []models.InlineKeyboardButton{
				{
					Text:         tm.GetText(langCode, "setup_auto_renewal_button"),
					CallbackData: "auto_renewal_setup_start",
				},
			})
		}
	}

	// Кнопка "Назад" всегда присутствует
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{
			Text:         tm.GetText(langCode, "back_button"),
			CallbackData: CallbackStart,
		},
	})

	return models.InlineKeyboardMarkup{InlineKeyboard: keyboard}
}

// resolveConnectButton - создает кнопку "Подключиться" аналогично start.go
func resolveConnectButton(customer *database.Customer, lang string, tm *translation.Manager) []models.InlineKeyboardButton {
	var inlineKeyboard []models.InlineKeyboardButton

	var url string
	if customer != nil && customer.SubscriptionLink != nil && *customer.SubscriptionLink != "" {
		url = *customer.SubscriptionLink
	} else if config.GetMiniAppURL() != "" {
		url = config.GetMiniAppURL()
	}

	if url != "" {
		inlineKeyboard = []models.InlineKeyboardButton{
			{Text: tm.GetText(lang, "connect_button"), WebApp: &models.WebAppInfo{URL: url}},
		}
	} else {
		inlineKeyboard = []models.InlineKeyboardButton{
			{Text: tm.GetText(lang, "connect_button"), CallbackData: CallbackConnect},
		}
	}
	return inlineKeyboard
}
