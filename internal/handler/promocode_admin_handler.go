package handler

import (
	"context"
	"fmt"
	"log/slog"
	"regexp"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/service"
	"strconv"
	"strings"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// FSM состояния для создания/редактирования промокодов
const (
	promoCodeAdminFSMStateCode            = 8001
	promoCodeAdminFSMStateDescription     = 8002
	promoCodeAdminFSMStateDiscount        = 8003
	promoCodeAdminFSMStateExpiryType      = 8004
	promoCodeAdminFSMStateExpiryValue     = 8005
	promoCodeAdminFSMStateActivationLimit = 8006
	promoCodeAdminFSMStateUserType        = 8007
	promoCodeAdminFSMStateTariffs         = 8008
	promoCodeAdminFSMStateConfirm         = 8009
)

// PromoCodeAdminHandler - административный обработчик промокодов
type PromoCodeAdminHandler struct {
	*AdminHandler
	promoCodeService *service.PromoCodeService
}

// NewPromoCodeAdminHandler создает новый административный обработчик промокодов
func NewPromoCodeAdminHandler(adminHandler *AdminHandler, promoCodeService *service.PromoCodeService) *PromoCodeAdminHandler {
	return &PromoCodeAdminHandler{
		AdminHandler:     adminHandler,
		promoCodeService: promoCodeService,
	}
}

// PromoCodeMessageHandler обрабатывает текстовые сообщения в FSM промокодов
func (h *PromoCodeAdminHandler) PromoCodeMessageHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	if update.Message == nil {
		return
	}

	userID := update.Message.From.ID
	msg := update.Message
	msgID, ok := h.cache.GetInt(9999999 + userID)
	if !ok {
		return
	}

	state, ok := h.cache.GetInt(userID)
	if !ok {
		return
	}

	switch state {
	case promoCodeAdminFSMStateCode:
		if msg.Text != "" {
			code := strings.TrimSpace(msg.Text)

			// Валидация кода промокода
			if len(code) == 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Код промокода не может быть пустым. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
						},
					},
				})
				return
			}

			// Проверяем формат кода (только английские буквы и цифры)
			validCodeRegex := regexp.MustCompile(`^[a-zA-Z0-9]+$`)
			if !validCodeRegex.MatchString(code) {
				// Удаляем сообщение администратора с неверным кодом
				b.DeleteMessage(ctx, &bot.DeleteMessageParams{
					ChatID:    msg.Chat.ID,
					MessageID: msg.ID,
				})

				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "❌ Код промокода должен содержать только английские буквы и цифры. Введите снова:",
					ParseMode: models.ParseModeHTML,
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
						},
					},
				})
				return
			}

			if len(code) > 50 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Код промокода не может быть длиннее 50 символов. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
						},
					},
				})
				return
			}

			// Проверяем, не существует ли уже такой промокод
			existingPromoCode, _ := h.promoCodeService.GetPromoCodeByCode(ctx, code)
			if existingPromoCode != nil {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Промокод с таким кодом уже существует. Введите другой код:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
						},
					},
				})
				return
			}

			h.cache.SetString(8000000+userID, code)
			h.cache.SetInt(userID, promoCodeAdminFSMStateDescription)
			b.DeleteMessage(ctx, &bot.DeleteMessageParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
			})
			_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Введите <b>описание промокода</b>:",
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
					},
				},
			})
			if err != nil {
				slog.Error("Ошибка шага ввода описания промокода", "error", err)
			}
		}
		return

	case promoCodeAdminFSMStateDescription:
		if msg.Text != "" {
			description := strings.TrimSpace(msg.Text)
			if len(description) == 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Описание промокода не может быть пустым. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
						},
					},
				})
				return
			}

			h.cache.SetString(8000001+userID, description)
			h.cache.SetInt(userID, promoCodeAdminFSMStateDiscount)
			b.DeleteMessage(ctx, &bot.DeleteMessageParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
			})
			_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Введите <b>процент скидки</b> (от 1 до 100):",
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
					},
				},
			})
			if err != nil {
				slog.Error("Ошибка шага ввода процента скидки", "error", err)
			}
		}
		return

	case promoCodeAdminFSMStateDiscount:
		if msg.Text != "" {
			discount, err := strconv.Atoi(msg.Text)
			if err != nil || discount < 1 || discount > 100 {
				// Удаляем сообщение администратора с некорректными данными
				b.DeleteMessage(ctx, &bot.DeleteMessageParams{
					ChatID:    msg.Chat.ID,
					MessageID: msg.ID,
				})

				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Процент скидки должен быть числом от 1 до 100. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
						},
					},
				})
				return
			}

			h.cache.SetInt(8000002+userID, discount)
			h.cache.SetInt(userID, promoCodeAdminFSMStateExpiryType)
			b.DeleteMessage(ctx, &bot.DeleteMessageParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
			})
			_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Выберите <b>тип срока действия</b>:",
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⏰ По времени (часы)", CallbackData: "promo_expiry_hours"}},
						{{Text: "📅 Количество дней", CallbackData: "promo_expiry_days"}},
						{{Text: "🌅 До конца дня", CallbackData: "promo_expiry_end_of_day"}},
						{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
					},
				},
			})
			if err != nil {
				slog.Error("Ошибка шага выбора типа срока действия", "error", err)
			}
		}
		return

	case promoCodeAdminFSMStateExpiryValue:
		if msg.Text != "" {
			value, err := strconv.Atoi(msg.Text)
			expiryType, _ := h.cache.GetString(8000003 + userID)

			// Проверяем базовые условия и ограничения
			var errorText string
			if err != nil || value <= 0 {
				var unit string
				if expiryType == "hours" {
					unit = "часов"
				} else {
					unit = "дней"
				}
				errorText = fmt.Sprintf("Количество %s должно быть положительным числом. Введите снова:", unit)
			} else if expiryType == "hours" && value > 168 {
				// Максимум 168 часов (7 дней)
				errorText = "Максимальный срок действия: 168 часов (7 дней). Введите снова:"
			} else if expiryType == "days" && value > 30 {
				// Максимум 30 дней
				errorText = "Максимальный срок действия: 30 дней. Введите снова:"
			}

			if errorText != "" {
				// Удаляем сообщение администратора с некорректными данными
				b.DeleteMessage(ctx, &bot.DeleteMessageParams{
					ChatID:    msg.Chat.ID,
					MessageID: msg.ID,
				})

				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      errorText,
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
						},
					},
				})
				return
			}

			h.cache.SetInt(8000004+userID, value)
			h.cache.SetInt(userID, promoCodeAdminFSMStateActivationLimit)
			b.DeleteMessage(ctx, &bot.DeleteMessageParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
			})
			_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Введите <b>лимит активаций</b> (0 = бесконечно):",
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
					},
				},
			})
			if err != nil {
				slog.Error("Ошибка шага ввода лимита активаций", "error", err)
			}
		}
		return

	case promoCodeAdminFSMStateActivationLimit:
		if msg.Text != "" {
			limit, err := strconv.Atoi(msg.Text)
			if err != nil || limit < 0 {
				// Удаляем сообщение администратора с некорректными данными
				b.DeleteMessage(ctx, &bot.DeleteMessageParams{
					ChatID:    msg.Chat.ID,
					MessageID: msg.ID,
				})

				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Лимит активаций должен быть неотрицательным числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
						},
					},
				})
				return
			}

			h.cache.SetInt(8000005+userID, limit)
			h.cache.SetInt(userID, promoCodeAdminFSMStateUserType)
			b.DeleteMessage(ctx, &bot.DeleteMessageParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
			})
			_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Выберите <b>тип пользователей</b>:",
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "✅ С активной подпиской", CallbackData: "promo_user_with_subscription"}},
						{{Text: "❌ Без активной подписки", CallbackData: "promo_user_without_subscription"}},
						{{Text: "👥 Все пользователи", CallbackData: "promo_user_all"}},
						{{Text: "❌ Отмена", CallbackData: "admin_promocodes"}},
					},
				},
			})
			if err != nil {
				slog.Error("Ошибка шага выбора типа пользователей", "error", err)
			}
		}
		return
	}
}

// showPromoCodesPage показывает страницу с промокодами
func (h *PromoCodeAdminHandler) showPromoCodesPage(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery, page int) {
	userID := callback.From.ID
	h.clearPromoCodeMessages(ctx, b, userID, callback.Message.Message.Chat.ID)

	promoCodes, err := h.promoCodeService.GetAllPromoCodes(ctx, false)
	if err != nil {
		slog.Error("Ошибка получения промокодов", "error", err)
		return
	}

	const promoCodesPerPage = 3
	total := len(promoCodes)
	totalPages := (total + promoCodesPerPage - 1) / promoCodesPerPage
	if page < 1 {
		page = 1
	}
	if page > totalPages {
		page = totalPages
	}

	start := (page - 1) * promoCodesPerPage
	end := start + promoCodesPerPage
	if end > total {
		end = total
	}

	var sentMessages []int

	if total == 0 {
		text := "Нет созданных промокодов."
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
		})
		if err == nil {
			sentMessages = append(sentMessages, msg.ID)
		}
	} else {
		pagedPromoCodes := promoCodes[start:end]
		for _, promoCode := range pagedPromoCodes {
			text, keyboard := h.buildPromoCodeEntry(promoCode, page)
			msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID:      callback.Message.Message.Chat.ID,
				Text:        text,
				ParseMode:   models.ParseModeHTML,
				ReplyMarkup: keyboard,
			})
			if err == nil {
				sentMessages = append(sentMessages, msg.ID)
			}
		}
	}

	// Пагинация и навигация
	var paginationKeyboard []models.InlineKeyboardButton
	if page > 1 && total > 0 {
		paginationKeyboard = append(paginationKeyboard, models.InlineKeyboardButton{Text: "◀️ Предыдущая", CallbackData: fmt.Sprintf("admin_promocodes_page_%d", page-1)})
	}
	paginationKeyboard = append(paginationKeyboard, models.InlineKeyboardButton{Text: "➕ Создать", CallbackData: "promocode_add"})
	if page < totalPages && total > 0 {
		paginationKeyboard = append(paginationKeyboard, models.InlineKeyboardButton{Text: "▶️ Следующая", CallbackData: fmt.Sprintf("admin_promocodes_page_%d", page+1)})
	}

	navKeyboard := [][]models.InlineKeyboardButton{paginationKeyboard, {{Text: "⬅️ Назад", CallbackData: "admin_menu"}}}

	var navText string
	if total == 0 {
		navText = "Управление промокодами"
	} else {
		navText = fmt.Sprintf("Страница %d из %d", page, totalPages)
	}

	msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      callback.Message.Message.Chat.ID,
		Text:        navText,
		ReplyMarkup: models.InlineKeyboardMarkup{InlineKeyboard: navKeyboard},
	})
	if err == nil {
		sentMessages = append(sentMessages, msg.ID)
	}

	// Сохраняем ID сообщений для последующего удаления
	h.cache.Set(8999999+userID, sentMessages)
}

// buildPromoCodeEntry создает текст и клавиатуру для промокода
func (h *PromoCodeAdminHandler) buildPromoCodeEntry(promoCode database.PromoCode, page int) (string, models.InlineKeyboardMarkup) {
	var status string
	if promoCode.Active {
		if promoCode.IsExpired() {
			status = "⏰ Истек"
		} else if promoCode.IsLimitReached() {
			status = "🚫 Лимит исчерпан"
		} else {
			status = "✅ Активен"
		}
	} else {
		status = "❌ Неактивен"
	}

	var expiryText string
	// Используем правильное форматирование времени как в системе рассылок
	switch promoCode.ExpiryType {
	case database.PromoCodeExpiryTypeHours:
		expiryText = fmt.Sprintf("до %s", FormatTimeWithTZ(promoCode.ExpiresAt))
	case database.PromoCodeExpiryTypeDays:
		expiryText = fmt.Sprintf("до %s", FormatTimeWithTZ(promoCode.ExpiresAt))
	case database.PromoCodeExpiryTypeEndOfDay:
		expiryText = fmt.Sprintf("до %s", FormatTimeWithTZ(promoCode.ExpiresAt))
	}

	var userTypeText string
	switch promoCode.UserType {
	case database.PromoCodeUserTypeWithSubscription:
		userTypeText = "С подпиской"
	case database.PromoCodeUserTypeWithoutSubscription:
		userTypeText = "Без подписки"
	case database.PromoCodeUserTypeAll:
		userTypeText = "Все"
	}

	var tariffsText string
	if promoCode.ApplicableTariffs.All {
		tariffsText = "Все тарифы"
	} else {
		tariffsText = strings.Join(promoCode.ApplicableTariffs.Codes, ", ")
	}

	var limitText string
	if promoCode.ActivationLimit == 0 {
		limitText = "∞"
	} else {
		limitText = fmt.Sprintf("%d/%d", promoCode.CurrentActivations, promoCode.ActivationLimit)
	}

	text := fmt.Sprintf(
		"<b>Промокод %s</b>\n\n"+
			"Описание: %s\n"+
			"Скидка: <b>%d%%</b>\n"+
			"Статус: %s\n"+
			"Срок действия: %s\n"+
			"Использований: %s\n"+
			"Пользователи: %s\n"+
			"Тарифы: %s",
		promoCode.Code, promoCode.Description, promoCode.DiscountPercent, status,
		expiryText, limitText, userTypeText, tariffsText,
	)

	var toggleButtonText string
	if promoCode.Active {
		toggleButtonText = "❌ Выключить"
	} else {
		toggleButtonText = "✅ Включить"
	}

	keyboard := models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{{Text: toggleButtonText, CallbackData: fmt.Sprintf("promocode_toggle_%d_p%d", promoCode.ID, page)}},
			{{Text: "📊 Статистика", CallbackData: fmt.Sprintf("promocode_stats_%d_p%d", promoCode.ID, page)}},
			{{Text: "🗑️ Удалить", CallbackData: fmt.Sprintf("promocode_delete_%d_p%d", promoCode.ID, page)}},
		},
	}

	return text, keyboard
}

// clearPromoCodeMessages очищает сообщения промокодов
func (h *PromoCodeAdminHandler) clearPromoCodeMessages(ctx context.Context, b *bot.Bot, userID, chatID int64, exceptMsgID ...int) {
	if oldMsgIDs, ok := h.cache.Get(8999999 + userID); ok {
		if ids, ok := oldMsgIDs.([]int); ok {
			for _, id := range ids {
				skip := false
				for _, except := range exceptMsgID {
					if id == except {
						skip = true
						break
					}
				}
				if skip {
					continue
				}
				b.DeleteMessage(ctx, &bot.DeleteMessageParams{
					ChatID:    chatID,
					MessageID: id,
				})
			}
		}
		h.cache.Delete(8999999 + userID)
	}
}
