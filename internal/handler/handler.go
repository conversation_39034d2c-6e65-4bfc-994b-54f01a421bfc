package handler

import (
	"context"
	"log/slog"
	"remnawave-tg-shop-bot/internal/cache"

	"remnawave-tg-shop-bot/internal/cryptopay"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/payment"
	"remnawave-tg-shop-bot/internal/service"
	"remnawave-tg-shop-bot/internal/sync"
	"remnawave-tg-shop-bot/internal/translation"
	"remnawave-tg-shop-bot/internal/yookasa"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

type Handler struct {
	*AdminHandler
	*BroadcastHandler
	*TariffHandler
	*CallbackHandler
	*PromoCodeHandler
	*PromoCodeAdminHandler
	autoRenewalService *service.AutoRenewalService
	// Добавляем другие обработчики сюда
}

func <PERSON><PERSON>ler(
	syncService *sync.SyncService,
	paymentService *payment.PaymentService,
	translation *translation.Manager,
	customerRepository *database.CustomerRepository,
	purchaseRepository *database.PurchaseRepository,
	cryptoPayClient *cryptopay.Client,
	yookasaClient *yookasa.Client,
	referralRepository *database.ReferralRepository,
	cache *cache.Cache,
	broadcastTaskService *service.BroadcastTaskService,
	broadcastWakeup chan struct{},
	tariffRepository *database.TariffRepository,
	promoCodeService *service.PromoCodeService,
	autoRenewalService *service.AutoRenewalService,
) *Handler {
	adminHandler := NewAdminHandler(
		syncService,
		paymentService,
		translation,
		customerRepository,
		purchaseRepository,
		cryptoPayClient,
		yookasaClient,
		referralRepository,
		cache,
		broadcastTaskService,
		broadcastWakeup,
		tariffRepository,
	)

	broadcastHandler := NewBroadcastHandler(adminHandler)
	tariffHandler := NewTariffHandler(adminHandler)
	promoCodeHandler := NewPromoCodeHandler(adminHandler, promoCodeService)
	promoCodeAdminHandler := NewPromoCodeAdminHandler(adminHandler, promoCodeService)
	callbackHandler := NewCallbackHandler(adminHandler, broadcastHandler, tariffHandler, promoCodeAdminHandler)

	return &Handler{
		AdminHandler:          adminHandler,
		BroadcastHandler:      broadcastHandler,
		TariffHandler:         tariffHandler,
		CallbackHandler:       callbackHandler,
		PromoCodeHandler:      promoCodeHandler,
		PromoCodeAdminHandler: promoCodeAdminHandler,
		autoRenewalService:    autoRenewalService,
	}
}

// AdminTextHandler — универсальный обработчик текстовых сообщений от администратора.
func (h *Handler) AdminTextHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	if update == nil || update.Message == nil {
		slog.Error("[AdminTextHandler] update or message is nil", "update", update)
		return
	}
	userID := update.Message.From.ID
	state, ok := h.cache.GetInt(userID)
	if !ok {
		// Если состояния нет, то и обрабатывать нечего
		return
	}

	// Маршрутизация в зависимости от состояния FSM
	if (state >= tariffFSMStateCode && state <= tariffFSMStateConfirm) || (state >= tariffFSMStateEditTitle && state <= tariffFSMStateEditConfirm) {
		h.TariffHandler.AdminTariffTextHandler(ctx, b, update)
	} else if (state >= adminBroadcastStateCreateText && state <= adminBroadcastStateCreateReady) || (state >= adminBroadcastStateEditText && state <= adminBroadcastStateEditReady) || (state >= adminBroadcastStateInstantText && state <= adminBroadcastStateInstantTargetSelect) {
		h.BroadcastHandler.AdminBroadcastTextHandler(ctx, b, update)
	} else if state >= promoCodeAdminFSMStateCode && state <= promoCodeAdminFSMStateConfirm {
		h.PromoCodeAdminHandler.PromoCodeMessageHandler(ctx, b, update)
	} else if state == promoCodeFSMStateInput {
		h.PromoCodeHandler.PromoCodeInputHandler(ctx, b, update)
	}
}
