package handler

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"log/slog"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"

	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/database"
)

func (h Handler) BuyCallbackHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode
	userID := update.CallbackQuery.From.ID

	// Проверяем, есть ли сохраненный промокод в кэше
	if savedPromoCode, ok := h.cache.GetString(6000001 + userID); ok && savedPromoCode != "" {
		// Если есть сохраненный промокод, показываем тарифы с промокодом
		// Используем существующий CallbackHandler из структуры Handler
		if h.Callback<PERSON>andler != nil {
			h.CallbackHandler.showTariffsForPurchase(ctx, b, update.CallbackQuery, &savedPromoCode)
			return
		}
	}

	// Новая логика: сначала спрашиваем о промокоде
	text := "🎟️ Использовать промокод?\n\nУ вас есть промокод на скидку?"
	keyboard := [][]models.InlineKeyboardButton{
		{{Text: "✅ Да, у меня есть промокод", CallbackData: "buy_promo_yes"}},
		{{Text: "❌ Нет, продолжить без промокода", CallbackData: "buy_promo_no"}},
		{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackStart}},
	}

	_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: keyboard,
		},
		Text: text,
	})

	if err != nil {
		slog.Error("Error sending buy message", "error", err)
	}
}

func (h Handler) SellCallbackHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	callbackQuery := parseCallbackData(update.CallbackQuery.Data)
	langCode := update.CallbackQuery.From.LanguageCode

	if code, ok := callbackQuery["code"]; ok && code != "" {
		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil || tariff == nil {
			slog.Error("Ошибка поиска тарифа по коду", "code", code, "err", err)
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Chat.ID,
				MessageID: callback.ID,
				Text:      h.translation.GetText(langCode, "tariff_not_found"),
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackBuy}},
					},
				},
			})
			return
		}

		// Проверяем, есть ли промокод в callback data
		promoCode, hasPromo := callbackQuery["promo"]

		// Показываем способы оплаты (с промокодом или без)
		h.showPaymentMethodsForTariff(ctx, b, update, tariff, promoCode, hasPromo)
	}
}

// showPaymentMethodsForTariff показывает способы оплаты для выбранного тарифа
func (h Handler) showPaymentMethodsForTariff(ctx context.Context, b *bot.Bot, update *models.Update, tariff *database.Tariff, promoCode string, hasPromo bool) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	// Рассчитываем цены (с промокодом или без)
	var priceRUB, priceStars int
	var discountText string

	if hasPromo && promoCode != "" {
		// Получаем промокод из БД для расчета скидки через PromoCodeHandler
		validPromoCode, err := h.PromoCodeHandler.promoCodeService.GetPromoCodeByCode(ctx, promoCode)
		if err == nil && validPromoCode != nil {
			// Рассчитываем цены со скидкой
			_, priceRUB = h.PromoCodeHandler.promoCodeService.CalculateDiscountedPrice(tariff.PriceRUB, validPromoCode.DiscountPercent, "RUB")
			_, priceStars = h.PromoCodeHandler.promoCodeService.CalculateDiscountedPrice(tariff.PriceStars, validPromoCode.DiscountPercent, "STARS")

			discountText = fmt.Sprintf("\n\n🎉 <b>Применена скидка: %d%%</b>", validPromoCode.DiscountPercent)
		} else {
			// Если промокод не найден, используем обычные цены
			priceRUB = tariff.PriceRUB
			priceStars = tariff.PriceStars
		}
	} else {
		priceRUB = tariff.PriceRUB
		priceStars = tariff.PriceStars
	}

	// Формируем клавиатуру с способами оплаты
	var keyboard [][]models.InlineKeyboardButton

	// Добавляем способы оплаты с проверкой доступности
	if config.IsYookasaEnabled() && priceRUB > 0 {
		cardText := fmt.Sprintf(h.translation.GetText(langCode, "card_button"), priceRUB)
		callbackData := fmt.Sprintf("%s?code=%s", CallbackPayment, tariff.Code)
		if hasPromo && promoCode != "" {
			callbackData += "&promo=" + promoCode
		}
		callbackData += "&invoiceType=" + string(database.InvoiceTypeYookasa)
		keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: cardText, CallbackData: callbackData}})
	}

	if config.IsTelegramStarsEnabled() && priceStars > 0 {
		starsText := fmt.Sprintf(h.translation.GetText(langCode, "stars_button"), priceStars)
		callbackData := fmt.Sprintf("%s?code=%s", CallbackPayment, tariff.Code)
		if hasPromo && promoCode != "" {
			callbackData += "&promo=" + promoCode
		}
		callbackData += "&invoiceType=" + string(database.InvoiceTypeTelegram)
		keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: starsText, CallbackData: callbackData}})
	}

	if config.IsCryptoPayEnabled() && priceRUB > 0 {
		cryptoText := fmt.Sprintf(h.translation.GetText(langCode, "crypto_button"), priceRUB)
		callbackData := fmt.Sprintf("%s?code=%s", CallbackPayment, tariff.Code)
		if hasPromo && promoCode != "" {
			callbackData += "&promo=" + promoCode
		}
		callbackData += "&invoiceType=" + string(database.InvoiceTypeCrypto)
		keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: cryptoText, CallbackData: callbackData}})
	}

	if config.GetTributeWebHookUrl() != "" {
		keyboard = append(keyboard, []models.InlineKeyboardButton{
			{Text: h.translation.GetText(langCode, "tribute_button"), URL: config.GetTributePaymentUrl()},
		})
	}

	// Добавляем кнопку "Назад" с сохранением промокода в кэше
	if hasPromo && promoCode != "" {
		// Сохраняем промокод в кэше для восстановления при возврате
		userID := update.CallbackQuery.From.ID
		h.cache.SetString(6000001+userID, promoCode)
	}

	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackBuy},
	})

	// Формируем текст сообщения
	text := fmt.Sprintf("💳 Выберите способ оплаты\n\nТариф: <b>%s</b>%s", tariff.Title, discountText)

	_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		Text:      text,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: keyboard,
		},
	})

	if err != nil {
		slog.Error("Ошибка показа способов оплаты", "error", err)
	}
}

func (h Handler) PaymentCallbackHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	callbackQuery := parseCallbackData(update.CallbackQuery.Data)
	langCode := update.CallbackQuery.From.LanguageCode

	if code, ok := callbackQuery["code"]; ok && code != "" {
		invoiceType := database.InvoiceType(callbackQuery["invoiceType"])
		promoCode := callbackQuery["promo"] // Получаем промокод из callback data
		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil || tariff == nil {
			slog.Error("Ошибка поиска тарифа по коду (оплата)", "code", code, "err", err)
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Chat.ID,
				MessageID: callback.ID,
				Text:      h.translation.GetText(langCode, "tariff_not_found"),
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackBuy}},
					},
				},
			})
			return
		}
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
		defer cancel()
		customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
		if err != nil {
			slog.Error("Error finding customer", err)
			return
		}
		if customer == nil {
			slog.Error("customer not exist", "chatID", callback.Chat.ID, "error", err)
			return
		}
		ctxWithUsername := context.WithValue(ctx, "username", update.CallbackQuery.From.Username)
		paymentURL, _, err := h.paymentService.CreatePurchaseByTariff(ctxWithUsername, tariff, customer, invoiceType, promoCode)
		if err != nil {
			slog.Error("Error creating payment by tariff", "error", err)
			return
		}

		// Определяем название способа оплаты
		var paymentMethodName string
		switch invoiceType {
		case database.InvoiceTypeYookasa:
			paymentMethodName = "Банковская карта"
		case database.InvoiceTypeTelegram:
			paymentMethodName = "Telegram Stars"
		case database.InvoiceTypeCrypto:
			paymentMethodName = "Криптовалюта"
		case database.InvoiceTypeTribute:
			paymentMethodName = "Tribute"
		default:
			paymentMethodName = "Неизвестный способ"
		}

		// Формируем callback data для кнопки "Назад" с учетом промокода
		backCallbackData := fmt.Sprintf("%s?code=%s", CallbackSell, code)
		if promoCode != "" {
			backCallbackData += "&promo=" + promoCode
		}

		keyboard := [][]models.InlineKeyboardButton{
			{{Text: h.translation.GetText(langCode, "pay_button"), URL: paymentURL}},
			{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: backCallbackData}},
		}

		// Обновляем текст сообщения с информацией о выбранном способе оплаты
		text := fmt.Sprintf("✅ Выбран способ оплаты: <b>%s</b>. Для продолжения нажмите кнопку «Оплатить»", paymentMethodName)

		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Error updating sell message (tariff)", err)
		}
	}
}

func (h Handler) PreCheckoutCallbackHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	_, err := b.AnswerPreCheckoutQuery(ctx, &bot.AnswerPreCheckoutQueryParams{
		PreCheckoutQueryID: update.PreCheckoutQuery.ID,
		OK:                 true,
	})
	if err != nil {
		slog.Error("Error sending answer pre checkout query", err)
	}
}

func (h Handler) SuccessPaymentHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	payload := strings.Split(update.Message.SuccessfulPayment.InvoicePayload, "&")
	purchaseId, err := strconv.Atoi(payload[0])
	username := payload[1]
	if err != nil {
		slog.Error("Error parsing purchase id", err)
		return
	}

	ctxWithUsername := context.WithValue(ctx, "username", username)
	err = h.paymentService.ProcessPurchaseById(ctxWithUsername, int64(purchaseId))
	if err != nil {
		slog.Error("Error processing purchase", err)
	}
}

func parseCallbackData(data string) map[string]string {
	result := make(map[string]string)

	parts := strings.Split(data, "?")
	if len(parts) < 2 {
		return result
	}

	params := strings.Split(parts[1], "&")
	for _, param := range params {
		kv := strings.SplitN(param, "=", 2)
		if len(kv) == 2 {
			result[kv[0]] = kv[1]
		}
	}

	return result
}
