package handler

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/database"
	"strconv"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

const (
	tariffFSMStateCode           = 4001
	tariffFSMStateTitle          = 4002
	tariffFSMStatePriceRUB       = 4003
	tariffFSMStatePriceStars     = 4004
	tariffFSMStateConfirm        = 4006
	tariffFSMStateEditTitle      = 4007
	tariffFSMStateEditPriceRUB   = 4008
	tariffFSMStateEditPriceStars = 4009
	tariffFSMStateEditConfirm    = 4010
)

type TariffHandler struct {
	*AdminHandler
}

func NewTariffHandler(adminHandler *AdminHandler) *TariffHandler {
	return &TariffHandler{AdminHandler: adminHandler}
}

func (h *TariffHandler) AdminTariffTextHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	if update == nil || update.Message == nil {
		slog.Error("[AdminTariffTextHandler] update or message is nil", "update", update)
		return
	}
	userID := update.Message.From.ID
	state, ok := h.cache.GetInt(userID)
	if !ok {
		slog.Warn("[AdminTariffTextHandler] FSM state not found", "user_id", userID)
		return
	}
	msgID, ok := h.cache.GetInt(9999999 + userID)
	if !ok {
		slog.Warn("[AdminTariffTextHandler] message id not found", "user_id", userID)
		return
	}
	msg := update.Message

	switch state {
	case tariffFSMStateEditTitle:
		title := msg.Text
		if len(title) == 0 || len(title) > 64 {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Название тарифа не должно быть пустым и не должно превышать 64 символа. Введите снова:",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		h.cache.SetString(7000001+userID, title)
		h.cache.SetInt(userID, tariffFSMStateEditPriceRUB)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>новую цену в рублях</b>:",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода цены в рублях", err)
		}
		return
	case tariffFSMStateEditPriceRUB:
		if msg.Text != "" {
			priceRUB, err := strconv.Atoi(msg.Text)
			if err != nil || priceRUB <= 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Цена в рублях должна быть положительным целым числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			h.cache.SetInt(7000002+userID, priceRUB)
		}
		h.cache.SetInt(userID, tariffFSMStateEditPriceStars)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>новую цену в звёздах</b>:",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода цены в звёздах", err)
		}
		return
	case tariffFSMStateEditPriceStars:
		if msg.Text != "" {
			priceStars, err := strconv.Atoi(msg.Text)
			if err != nil || priceStars <= 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Цена в звёздах должна быть положительным целым числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			h.cache.SetInt(7000003+userID, priceStars)
		}
		h.cache.SetInt(userID, tariffFSMStateEditConfirm)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		code, _ := h.cache.GetString(7000000 + userID)
		title, _ := h.cache.GetString(7000001 + userID)
		priceRUB, _ := h.cache.GetInt(7000002 + userID)
		priceStars, _ := h.cache.GetInt(7000003 + userID)
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      fmt.Sprintf("Сохранить изменения тарифа?\nКод: <code>%s</code>\nНазвание: %s\nЦена: %d₽ / %d⭐", code, title, priceRUB, priceStars),
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "💾 Сохранить", CallbackData: "tariff_update"}, {Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага подтверждения редактирования тарифа", err)
		}
		return
	case tariffFSMStateCode:
		code := msg.Text
		if len(code) < 2 || len(code) > 16 {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Код тарифа должен быть от 2 до 16 символов. Введите снова:",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		for _, c := range code {
			if !(c >= 'a' && c <= 'z') && !(c >= 'A' && c <= 'Z') && !(c >= '0' && c <= '9') && c != '_' && c != '-' {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Код может содержать только латинские буквы, цифры, -, _ . Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
		}
		tariff, _ := h.tariffRepository.GetByCode(ctx, code)
		if tariff != nil {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Тариф с таким кодом уже существует. Введите другой код:",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}

		h.cache.SetString(7000000+userID, code)
		h.cache.SetInt(userID, tariffFSMStateTitle)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>название тарифа</b> (до 64 символов):",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода названия тарифа", err)
		}
		return
	case tariffFSMStateTitle:
		title := msg.Text
		if len(title) == 0 || len(title) > 64 {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Название тарифа не должно быть пустым и не должно превышать 64 символа. Введите снова:",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		h.cache.SetString(7000001+userID, title)
		h.cache.SetInt(userID, tariffFSMStatePriceRUB)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>цену в рублях</b>:",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода цены в рублях", err)
		}
		return
	case tariffFSMStatePriceRUB:
		if msg.Text != "" {
			priceRUB, err := strconv.Atoi(msg.Text)
			if err != nil || priceRUB <= 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Цена в рублях должна быть положительным целым числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			h.cache.SetInt(7000002+userID, priceRUB)
		}
		h.cache.SetInt(userID, tariffFSMStatePriceStars)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>цену в звёздах</b>:",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода цены в звёздах", err)
		}
		return
	case tariffFSMStatePriceStars:
		if msg.Text != "" {
			priceStars, err := strconv.Atoi(msg.Text)
			if err != nil || priceStars <= 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Цена в звёздах должна быть положительным целым числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			h.cache.SetInt(7000003+userID, priceStars)
		}
		h.cache.SetInt(userID, tariffFSMStateConfirm)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		code, _ := h.cache.GetString(7000000 + userID)
		title, _ := h.cache.GetString(7000001 + userID)
		priceRUB, _ := h.cache.GetInt(7000002 + userID)
		priceStars, _ := h.cache.GetInt(7000003 + userID)
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      fmt.Sprintf("Установите <b>статус тарифа</b>:\nКод: <code>%s</code>\nНазвание: %s\nЦена: %d₽ / %d⭐", code, title, priceRUB, priceStars),
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "💾 Создать", CallbackData: "tariff_create"}, {Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага выбора статуса тарифа", err)
		}
		return
	}
}

const tariffsPerPage = 3

func (h *TariffHandler) showTariffsPage(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery, page int) {
	userID := callback.From.ID
	h.clearTariffMessages(ctx, b, userID, callback.Message.Message.Chat.ID)

	tariffs, err := h.tariffRepository.GetAll(ctx, false)
	if err != nil {
		return
	}

	total := len(tariffs)
	totalPages := (total + tariffsPerPage - 1) / tariffsPerPage
	if page < 1 {
		page = 1
	}
	if page > totalPages {
		page = totalPages
	}

	start := (page - 1) * tariffsPerPage
	end := start + tariffsPerPage
	if end > total {
		end = total
	}

	pagedTariffs := tariffs[start:end]

	var sentMessages []int

	if len(pagedTariffs) == 0 {
		text := "Нет созданных тарифов."
		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "➕ Создать", CallbackData: "tariff_add"}},
			{{Text: "⬅️ Назад", CallbackData: "admin_menu"}},
		}
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:      callback.Message.Message.Chat.ID,
			Text:        text,
			ParseMode:   models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{InlineKeyboard: keyboard},
		})
		if err == nil {
			sentMessages = append(sentMessages, msg.ID)
		}
	} else {
		for _, tariff := range pagedTariffs {
			text, keyboard := buildTariffEntry(tariff, page)
			msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID:      callback.Message.Message.Chat.ID,
				Text:        text,
				ParseMode:   models.ParseModeHTML,
				ReplyMarkup: keyboard,
			})
			if err == nil {
				sentMessages = append(sentMessages, msg.ID)
			}
		}
	}

	var paginationKeyboard []models.InlineKeyboardButton
	if page > 1 {
		paginationKeyboard = append(paginationKeyboard, models.InlineKeyboardButton{Text: "◀️ Предыдущая", CallbackData: fmt.Sprintf("admin_tariffs_page_%d", page-1)})
	}
	paginationKeyboard = append(paginationKeyboard, models.InlineKeyboardButton{Text: "➕ Создать", CallbackData: "tariff_add"})
	if page < totalPages {
		paginationKeyboard = append(paginationKeyboard, models.InlineKeyboardButton{Text: "▶️ Следующая", CallbackData: fmt.Sprintf("admin_tariffs_page_%d", page+1)})
	}

	navKeyboard := [][]models.InlineKeyboardButton{paginationKeyboard, {{Text: "⬅️ Назад", CallbackData: "admin_menu"}}}
	msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      callback.Message.Message.Chat.ID,
		Text:        fmt.Sprintf("Страница %d из %d", page, totalPages),
		ReplyMarkup: models.InlineKeyboardMarkup{InlineKeyboard: navKeyboard},
	})
	if err == nil {
		sentMessages = append(sentMessages, msg.ID)
	}

	h.cache.Set(8000000+userID, sentMessages)
}

func (h *TariffHandler) clearTariffMessages(ctx context.Context, b *bot.Bot, userID, chatID int64, exceptMsgID ...int) {
	if oldMsgIDs, ok := h.cache.Get(8000000 + userID); ok {
		if ids, ok := oldMsgIDs.([]int); ok {
			for _, id := range ids {
				skip := false
				for _, except := range exceptMsgID {
					if id == except {
						skip = true
						break
					}
				}
				if skip {
					continue
				}
				b.DeleteMessage(ctx, &bot.DeleteMessageParams{
					ChatID:    chatID,
					MessageID: id,
				})
			}
		}
		h.cache.Delete(8000000 + userID)
	}
}

func buildTariffEntry(tariff database.Tariff, page int) (string, models.InlineKeyboardMarkup) {
	var status string
	if tariff.Active {
		status = "✅ Активен"
	} else {
		status = "❌ Неактивен"
	}

	text := fmt.Sprintf(
		"<b>Тариф %s</b>\n\nНазвание: %s\nСтатус: %s\nАудитория: Всем пользователям\nЦена: %d₽ / %d⭐",
		tariff.Code, tariff.Title, status, tariff.PriceRUB, tariff.PriceStars,
	)

	var toggleButtonText string
	if tariff.Active {
		toggleButtonText = "❌ Выключить"
	} else {
		toggleButtonText = "✅ Включить"
	}

	keyboard := [][]models.InlineKeyboardButton{
		{
			{Text: "✏️ Редактировать", CallbackData: fmt.Sprintf("tariff_edit_%s_p%d", tariff.Code, page)},
			{Text: "🗑 Удалить", CallbackData: fmt.Sprintf("tariff_delete_%s_p%d", tariff.Code, page)},
		},
		{
			{Text: toggleButtonText, CallbackData: fmt.Sprintf("tariff_toggle_%s_p%d", tariff.Code, page)},
		},
	}

	return text, models.InlineKeyboardMarkup{InlineKeyboard: keyboard}
}

func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}
