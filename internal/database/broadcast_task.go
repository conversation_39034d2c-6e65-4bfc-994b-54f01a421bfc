package database

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	sq "github.com/Masterminds/squirrel"
	"github.com/jackc/pgx/v4/pgxpool"
)

type BroadcastTaskStatus string

const (
	BroadcastTaskStatusPending   BroadcastTaskStatus = "pending"
	BroadcastTaskStatusSent      BroadcastTaskStatus = "sent"
	BroadcastTaskStatusCancelled BroadcastTaskStatus = "cancelled"
)

type BroadcastTask struct {
	ID             int64               `db:"id"`
	Message        string              `db:"message_text"`
	SendAt         time.Time           `db:"send_at"`
	Status         BroadcastTaskStatus `db:"status"`
	TargetAudience string              `db:"target_audience"`
}

type BroadcastTaskRepository struct {
	pool *pgxpool.Pool
}

func NewBroadcastTaskRepository(pool *pgxpool.Pool) *BroadcastTaskRepository {
	return &BroadcastTaskRepository{pool: pool}
}

func (r *BroadcastTaskRepository) Create(ctx context.Context, task *BroadcastTask) (int64, error) {
	buildInsert := sq.Insert("broadcast_task").
		Columns("message_text", "send_at", "status", "target_audience").
		Values(task.Message, task.SendAt, task.Status, task.TargetAudience).
		Suffix("RETURNING id").
		PlaceholderFormat(sq.Dollar)
	sqlStr, args, err := buildInsert.ToSql()
	if err != nil {
		return 0, fmt.Errorf("failed to build insert query: %w", err)
	}
	row := r.pool.QueryRow(ctx, sqlStr, args...)
	var id int64
	if err := row.Scan(&id); err != nil {
		return 0, fmt.Errorf("failed to insert broadcast_task: %w", err)
	}
	task.ID = id
	return id, nil
}

func (r *BroadcastTaskRepository) GetByID(ctx context.Context, id int64) (*BroadcastTask, error) {
	buildSelect := sq.Select("id", "message_text", "send_at", "status", "target_audience").
		From("broadcast_task").
		Where(sq.Eq{"id": id}).
		PlaceholderFormat(sq.Dollar)
	sqlStr, args, err := buildSelect.ToSql()
	if err != nil {
		return nil, fmt.Errorf("failed to build select query: %w", err)
	}
	row := r.pool.QueryRow(ctx, sqlStr, args...)
	task := &BroadcastTask{}
	if err := row.Scan(&task.ID, &task.Message, &task.SendAt, &task.Status, &task.TargetAudience); err != nil {
		return nil, err
	}
	return task, nil
}

func (r *BroadcastTaskRepository) GetAll(ctx context.Context) ([]BroadcastTask, error) {
	buildSelect := sq.Select("id", "message_text", "send_at", "status", "target_audience").
		From("broadcast_task").
		OrderBy("send_at ASC").
		PlaceholderFormat(sq.Dollar)
	sqlStr, args, err := buildSelect.ToSql()
	if err != nil {
		return nil, fmt.Errorf("failed to build select query: %w", err)
	}
	rows, err := r.pool.Query(ctx, sqlStr, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query broadcast_tasks: %w", err)
	}
	defer rows.Close()
	var tasks []BroadcastTask
	for rows.Next() {
		task := BroadcastTask{}
		if err := rows.Scan(&task.ID, &task.Message, &task.SendAt, &task.Status, &task.TargetAudience); err != nil {
			return nil, err
		}
		tasks = append(tasks, task)
	}
	return tasks, nil
}

func (r *BroadcastTaskRepository) UpdateStatus(ctx context.Context, id int64, status BroadcastTaskStatus) error {
	buildUpdate := sq.Update("broadcast_task").
		Set("status", status).
		Where(sq.Eq{"id": id}).
		PlaceholderFormat(sq.Dollar)
	sqlStr, args, err := buildUpdate.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build update query: %w", err)
	}
	_, err = r.pool.Exec(ctx, sqlStr, args...)
	return err
}

func (r *BroadcastTaskRepository) Delete(ctx context.Context, id int64) error {
	buildDelete := sq.Delete("broadcast_task").
		Where(sq.Eq{"id": id}).
		PlaceholderFormat(sq.Dollar)
	sqlStr, args, err := buildDelete.ToSql()
	if err != nil {
		return fmt.Errorf("failed to build delete query: %w", err)
	}

	slog.Info("Executing delete query", "sql", sqlStr, "args", args)

	cmdTag, err := r.pool.Exec(ctx, sqlStr, args...)
	if err != nil {
		slog.Error("Failed to execute delete query", "err", err)
		return err
	}

	rowsAffected := cmdTag.RowsAffected()
	slog.Info("Delete query executed", "rows_affected", rowsAffected)

	if rowsAffected == 0 {
		return fmt.Errorf("no rows deleted for id %d", id)
	}

	return nil
}
