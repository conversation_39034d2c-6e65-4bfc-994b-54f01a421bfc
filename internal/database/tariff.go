package database

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"time"

	sq "github.com/Masterminds/squirrel"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
)

// Модель тарифа
// Соответствует таблице tariffs
type Tariff struct {
	ID         int64     `db:"id"`
	Code       string    `db:"code"`
	Title      string    `db:"title"`
	PriceRUB   int       `db:"price_rub"`
	PriceStars int       `db:"price_stars"`
	Active     bool      `db:"active"`
	CreatedAt  time.Time `db:"created_at"`
	UpdatedAt  time.Time `db:"updated_at"`
}

// GetMonthsFromCode извлекает количество месяцев из кода тарифа
// Например: "1m" -> 1, "3m" -> 3, "6m" -> 6, "12m" -> 12
func (t *Tariff) GetMonthsFromCode() int {
	// Удаляем все символы кроме цифр
	re := regexp.MustCompile(`\d+`)
	matches := re.FindString(t.Code)

	if matches == "" {
		// Если не найдены цифры, возвращаем 1 по умолчанию
		return 1
	}

	months, err := strconv.Atoi(matches)
	if err != nil {
		// Если ошибка парсинга, возвращаем 1 по умолчанию
		return 1
	}

	return months
}

// Репозиторий для работы с тарифами
type TariffRepository struct {
	pool *pgxpool.Pool
}

func NewTariffRepository(pool *pgxpool.Pool) *TariffRepository {
	return &TariffRepository{pool: pool}
}

// Получить все тарифы (если onlyActive=true — только активные)
func (r *TariffRepository) GetAll(ctx context.Context, onlyActive bool) ([]Tariff, error) {
	builder := sq.Select("id", "code", "title", "price_rub", "price_stars", "active", "created_at", "updated_at").From("tariffs").PlaceholderFormat(sq.Dollar)
	if onlyActive {
		builder = builder.Where(sq.Eq{"active": true})
	}
	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	rows, err := r.pool.Query(ctx, sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var tariffs []Tariff
	for rows.Next() {
		t := Tariff{}
		err := rows.Scan(&t.ID, &t.Code, &t.Title, &t.PriceRUB, &t.PriceStars, &t.Active, &t.CreatedAt, &t.UpdatedAt)
		if err != nil {
			return nil, err
		}
		tariffs = append(tariffs, t)
	}
	return tariffs, nil
}

// Получить тариф по коду
func (r *TariffRepository) GetByCode(ctx context.Context, code string) (*Tariff, error) {
	builder := sq.Select("id", "code", "title", "price_rub", "price_stars", "active", "created_at", "updated_at").From("tariffs").Where(sq.Eq{"code": code}).PlaceholderFormat(sq.Dollar)
	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	row := r.pool.QueryRow(ctx, sqlStr, args...)
	t := &Tariff{}
	err = row.Scan(&t.ID, &t.Code, &t.Title, &t.PriceRUB, &t.PriceStars, &t.Active, &t.CreatedAt, &t.UpdatedAt)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, nil // Тариф не найден, ошибки нет
		}
		return nil, err // Другая ошибка
	}
	return t, nil
}

// Создать новый тариф
func (r *TariffRepository) Create(ctx context.Context, tariff *Tariff) (int64, error) {
	builder := sq.Insert("tariffs").
		Columns("code", "title", "price_rub", "price_stars", "active").
		Values(tariff.Code, tariff.Title, tariff.PriceRUB, tariff.PriceStars, tariff.Active).
		Suffix("RETURNING id").
		PlaceholderFormat(sq.Dollar)
	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return 0, err
	}
	var id int64
	err = r.pool.QueryRow(ctx, sqlStr, args...).Scan(&id)
	if err != nil {
		return 0, err
	}
	return id, nil
}

// Обновить тариф (по id)
func (r *TariffRepository) Update(ctx context.Context, tariff *Tariff) error {
	builder := sq.Update("tariffs").
		Set("title", tariff.Title).
		Set("price_rub", tariff.PriceRUB).
		Set("price_stars", tariff.PriceStars).
		Set("active", tariff.Active).
		Set("updated_at", sq.Expr("NOW()")).
		Where(sq.Eq{"id": tariff.ID}).
		PlaceholderFormat(sq.Dollar)
	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return err
	}
	_, err = r.pool.Exec(ctx, sqlStr, args...)
	return err
}

// Активировать/деактивировать тариф
func (r *TariffRepository) SetActive(ctx context.Context, id int64, active bool) error {
	builder := sq.Update("tariffs").
		Set("active", active).
		Set("updated_at", sq.Expr("NOW()")).
		Where(sq.Eq{"id": id}).
		PlaceholderFormat(sq.Dollar)
	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return err
	}
	fmt.Printf("[SetActive] SQL: %s, args: %v\n", sqlStr, args)
	res, err := r.pool.Exec(ctx, sqlStr, args...)
	if err != nil {
		fmt.Printf("[SetActive] Exec error: %v\n", err)
		return err
	}
	fmt.Printf("[SetActive] Rows affected: %d\n", res.RowsAffected())
	return nil
}

// Удалить тариф по id
func (r *TariffRepository) Delete(ctx context.Context, id int64) error {
	builder := sq.Delete("tariffs").Where(sq.Eq{"id": id}).PlaceholderFormat(sq.Dollar)
	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return err
	}
	_, err = r.pool.Exec(ctx, sqlStr, args...)
	return err
}
