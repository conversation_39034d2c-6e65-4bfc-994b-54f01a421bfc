package database

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	sq "github.com/Masterminds/squirrel"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
)

// Типы данных для промокодов

// PromoCodeExpiryType - тип срока действия промокода
type PromoCodeExpiryType string

const (
	PromoCodeExpiryTypeHours    PromoCodeExpiryType = "hours"
	PromoCodeExpiryTypeDays     PromoCodeExpiryType = "days"
	PromoCodeExpiryTypeEndOfDay PromoCodeExpiryType = "end_of_day"
)

// PromoCodeUserType - тип пользователей, которые могут использовать промокод
type PromoCodeUserType string

const (
	PromoCodeUserTypeWithSubscription    PromoCodeUserType = "with_subscription"
	PromoCodeUserTypeWithoutSubscription PromoCodeUserType = "without_subscription"
	PromoCodeUserTypeAll                 PromoCodeUserType = "all"
)

// ApplicableTariffs - применимые тарифы (может быть "all" или список кодов тарифов)
type ApplicableTariffs struct {
	All   bool     `json:"-"`
	Codes []string `json:"codes,omitempty"`
}

// MarshalJSON реализует json.Marshaler
func (at ApplicableTariffs) MarshalJSON() ([]byte, error) {
	if at.All {
		return json.Marshal("all")
	}
	return json.Marshal(at.Codes)
}

// UnmarshalJSON реализует json.Unmarshaler
func (at *ApplicableTariffs) UnmarshalJSON(data []byte) error {
	var str string
	if err := json.Unmarshal(data, &str); err == nil {
		if str == "all" {
			at.All = true
			at.Codes = nil
			return nil
		}
		return fmt.Errorf("invalid string value for ApplicableTariffs: %s", str)
	}

	var codes []string
	if err := json.Unmarshal(data, &codes); err != nil {
		return err
	}
	at.All = false
	at.Codes = codes
	return nil
}

// Value реализует driver.Valuer для работы с PostgreSQL
func (at ApplicableTariffs) Value() (driver.Value, error) {
	return json.Marshal(at)
}

// Scan реализует sql.Scanner для работы с PostgreSQL
func (at *ApplicableTariffs) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into ApplicableTariffs", value)
	}

	return json.Unmarshal(bytes, at)
}

// IsApplicableToTariff проверяет, применим ли промокод к указанному тарифу
func (at ApplicableTariffs) IsApplicableToTariff(tariffCode string) bool {
	if at.All {
		return true
	}

	for _, code := range at.Codes {
		if code == tariffCode {
			return true
		}
	}
	return false
}

// PromoCode - модель промокода
type PromoCode struct {
	ID                 int64               `db:"id"`
	Code               string              `db:"code"`
	Description        string              `db:"description"`
	DiscountPercent    int                 `db:"discount_percent"`
	ExpiryType         PromoCodeExpiryType `db:"expiry_type"`
	ExpiryValue        *int                `db:"expiry_value"`
	ExpiresAt          time.Time           `db:"expires_at"`
	ActivationLimit    int                 `db:"activation_limit"`
	CurrentActivations int                 `db:"current_activations"`
	UserType           PromoCodeUserType   `db:"user_type"`
	ApplicableTariffs  ApplicableTariffs   `db:"applicable_tariffs"`
	Active             bool                `db:"active"`
	CreatedAt          time.Time           `db:"created_at"`
	UpdatedAt          time.Time           `db:"updated_at"`
	CreatedBy          *int64              `db:"created_by"`
}

// IsExpired проверяет, истек ли срок действия промокода
func (pc *PromoCode) IsExpired() bool {
	return time.Now().After(pc.ExpiresAt)
}

// IsLimitReached проверяет, достигнут ли лимит активаций
func (pc *PromoCode) IsLimitReached() bool {
	return pc.ActivationLimit > 0 && pc.CurrentActivations >= pc.ActivationLimit
}

// CanBeUsedBy проверяет, может ли пользователь использовать промокод
func (pc *PromoCode) CanBeUsedBy(customer *Customer) bool {
	hasActiveSubscription := customer.ExpireAt != nil && customer.ExpireAt.After(time.Now())

	switch pc.UserType {
	case PromoCodeUserTypeWithSubscription:
		return hasActiveSubscription
	case PromoCodeUserTypeWithoutSubscription:
		return !hasActiveSubscription
	case PromoCodeUserTypeAll:
		return true
	default:
		return false
	}
}

// PromoCodeUsage - модель использования промокода
type PromoCodeUsage struct {
	ID             int64     `db:"id"`
	PromoCodeID    int64     `db:"promocode_id"`
	CustomerID     int64     `db:"customer_id"`
	PurchaseID     int64     `db:"purchase_id"`
	TariffCode     string    `db:"tariff_code"`
	OriginalPrice  int       `db:"original_price"`
	DiscountAmount int       `db:"discount_amount"`
	FinalPrice     int       `db:"final_price"`
	Currency       string    `db:"currency"`
	UsedAt         time.Time `db:"used_at"`
}

// PromoCodeRepository - репозиторий для работы с промокодами
type PromoCodeRepository struct {
	pool *pgxpool.Pool
}

// NewPromoCodeRepository создает новый репозиторий промокодов
func NewPromoCodeRepository(pool *pgxpool.Pool) *PromoCodeRepository {
	return &PromoCodeRepository{pool: pool}
}

// GetByCode получает промокод по коду
func (r *PromoCodeRepository) GetByCode(ctx context.Context, code string) (*PromoCode, error) {
	builder := sq.Select("id", "code", "description", "discount_percent", "expiry_type", "expiry_value",
		"expires_at", "activation_limit", "current_activations", "user_type", "applicable_tariffs",
		"active", "created_at", "updated_at", "created_by").
		From("promocodes").
		Where(sq.Eq{"code": code}).
		PlaceholderFormat(sq.Dollar)

	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var pc PromoCode
	err = r.pool.QueryRow(ctx, sqlStr, args...).Scan(
		&pc.ID, &pc.Code, &pc.Description, &pc.DiscountPercent, &pc.ExpiryType, &pc.ExpiryValue,
		&pc.ExpiresAt, &pc.ActivationLimit, &pc.CurrentActivations, &pc.UserType, &pc.ApplicableTariffs,
		&pc.Active, &pc.CreatedAt, &pc.UpdatedAt, &pc.CreatedBy,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	return &pc, nil
}

// GetAll получает все промокоды (если onlyActive=true — только активные)
func (r *PromoCodeRepository) GetAll(ctx context.Context, onlyActive bool) ([]PromoCode, error) {
	builder := sq.Select("id", "code", "description", "discount_percent", "expiry_type", "expiry_value",
		"expires_at", "activation_limit", "current_activations", "user_type", "applicable_tariffs",
		"active", "created_at", "updated_at", "created_by").
		From("promocodes").
		OrderBy("created_at DESC").
		PlaceholderFormat(sq.Dollar)

	if onlyActive {
		builder = builder.Where(sq.Eq{"active": true})
	}

	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := r.pool.Query(ctx, sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var promocodes []PromoCode
	for rows.Next() {
		var pc PromoCode
		err := rows.Scan(
			&pc.ID, &pc.Code, &pc.Description, &pc.DiscountPercent, &pc.ExpiryType, &pc.ExpiryValue,
			&pc.ExpiresAt, &pc.ActivationLimit, &pc.CurrentActivations, &pc.UserType, &pc.ApplicableTariffs,
			&pc.Active, &pc.CreatedAt, &pc.UpdatedAt, &pc.CreatedBy,
		)
		if err != nil {
			return nil, err
		}
		promocodes = append(promocodes, pc)
	}

	return promocodes, nil
}

// Create создает новый промокод
func (r *PromoCodeRepository) Create(ctx context.Context, pc *PromoCode) (int64, error) {
	builder := sq.Insert("promocodes").
		Columns("code", "description", "discount_percent", "expiry_type", "expiry_value",
			"activation_limit", "user_type", "applicable_tariffs", "active", "created_by").
		Values(pc.Code, pc.Description, pc.DiscountPercent, pc.ExpiryType, pc.ExpiryValue,
			pc.ActivationLimit, pc.UserType, pc.ApplicableTariffs, pc.Active, pc.CreatedBy).
		Suffix("RETURNING id").
		PlaceholderFormat(sq.Dollar)

	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return 0, err
	}

	var id int64
	err = r.pool.QueryRow(ctx, sqlStr, args...).Scan(&id)
	if err != nil {
		return 0, err
	}

	return id, nil
}

// Update обновляет промокод
func (r *PromoCodeRepository) Update(ctx context.Context, pc *PromoCode) error {
	builder := sq.Update("promocodes").
		Set("description", pc.Description).
		Set("discount_percent", pc.DiscountPercent).
		Set("expiry_type", pc.ExpiryType).
		Set("expiry_value", pc.ExpiryValue).
		Set("activation_limit", pc.ActivationLimit).
		Set("user_type", pc.UserType).
		Set("applicable_tariffs", pc.ApplicableTariffs).
		Set("active", pc.Active).
		Where(sq.Eq{"id": pc.ID}).
		PlaceholderFormat(sq.Dollar)

	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return err
	}

	_, err = r.pool.Exec(ctx, sqlStr, args...)
	return err
}

// Delete удаляет промокод
func (r *PromoCodeRepository) Delete(ctx context.Context, id int64) error {
	builder := sq.Delete("promocodes").
		Where(sq.Eq{"id": id}).
		PlaceholderFormat(sq.Dollar)

	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return err
	}

	_, err = r.pool.Exec(ctx, sqlStr, args...)
	return err
}

// SetActive изменяет статус активности промокода
func (r *PromoCodeRepository) SetActive(ctx context.Context, id int64, active bool) error {
	builder := sq.Update("promocodes").
		Set("active", active).
		Where(sq.Eq{"id": id}).
		PlaceholderFormat(sq.Dollar)

	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return err
	}

	_, err = r.pool.Exec(ctx, sqlStr, args...)
	return err
}

// GetExpiringPromoCodes получает промокоды, которые истекают в течение указанного времени
func (r *PromoCodeRepository) GetExpiringPromoCodes(ctx context.Context, within time.Duration) ([]PromoCode, error) {
	expiryThreshold := time.Now().Add(within)

	builder := sq.Select("id", "code", "description", "discount_percent", "expiry_type", "expiry_value",
		"expires_at", "activation_limit", "current_activations", "user_type", "applicable_tariffs",
		"active", "created_at", "updated_at", "created_by").
		From("promocodes").
		Where(sq.And{
			sq.Eq{"active": true},
			sq.Lt{"expires_at": expiryThreshold},
			sq.Gt{"expires_at": time.Now()},
		}).
		OrderBy("expires_at ASC").
		PlaceholderFormat(sq.Dollar)

	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	rows, err := r.pool.Query(ctx, sqlStr, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var promocodes []PromoCode
	for rows.Next() {
		var pc PromoCode
		err := rows.Scan(
			&pc.ID, &pc.Code, &pc.Description, &pc.DiscountPercent, &pc.ExpiryType, &pc.ExpiryValue,
			&pc.ExpiresAt, &pc.ActivationLimit, &pc.CurrentActivations, &pc.UserType, &pc.ApplicableTariffs,
			&pc.Active, &pc.CreatedAt, &pc.UpdatedAt, &pc.CreatedBy,
		)
		if err != nil {
			return nil, err
		}
		promocodes = append(promocodes, pc)
	}

	return promocodes, nil
}

// PromoCodeUsageRepository - репозиторий для работы с использованиями промокодов
type PromoCodeUsageRepository struct {
	pool *pgxpool.Pool
}

// NewPromoCodeUsageRepository создает новый репозиторий использований промокодов
func NewPromoCodeUsageRepository(pool *pgxpool.Pool) *PromoCodeUsageRepository {
	return &PromoCodeUsageRepository{pool: pool}
}

// Create создает новое использование промокода
func (r *PromoCodeUsageRepository) Create(ctx context.Context, usage *PromoCodeUsage) (int64, error) {
	builder := sq.Insert("promocode_usages").
		Columns("promocode_id", "customer_id", "purchase_id", "tariff_code",
			"original_price", "discount_amount", "final_price", "currency").
		Values(usage.PromoCodeID, usage.CustomerID, usage.PurchaseID, usage.TariffCode,
			usage.OriginalPrice, usage.DiscountAmount, usage.FinalPrice, usage.Currency).
		Suffix("RETURNING id").
		PlaceholderFormat(sq.Dollar)

	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return 0, err
	}

	var id int64
	err = r.pool.QueryRow(ctx, sqlStr, args...).Scan(&id)
	if err != nil {
		return 0, err
	}

	return id, nil
}

// HasUsedPromoCode проверяет, использовал ли пользователь промокод
func (r *PromoCodeUsageRepository) HasUsedPromoCode(ctx context.Context, promoCodeID, customerID int64) (bool, error) {
	builder := sq.Select("COUNT(*)").
		From("promocode_usages").
		Where(sq.And{
			sq.Eq{"promocode_id": promoCodeID},
			sq.Eq{"customer_id": customerID},
		}).
		PlaceholderFormat(sq.Dollar)

	sqlStr, args, err := builder.ToSql()
	if err != nil {
		return false, err
	}

	var count int
	err = r.pool.QueryRow(ctx, sqlStr, args...).Scan(&count)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// GetStatistics получает статистику использования промокода
func (r *PromoCodeUsageRepository) GetStatistics(ctx context.Context, promoCodeID int64) (map[string]interface{}, error) {
	// Общее количество использований
	totalUsagesQuery := sq.Select("COUNT(*)").
		From("promocode_usages").
		Where(sq.Eq{"promocode_id": promoCodeID}).
		PlaceholderFormat(sq.Dollar)

	sqlStr, args, err := totalUsagesQuery.ToSql()
	if err != nil {
		return nil, err
	}

	var totalUsages int
	err = r.pool.QueryRow(ctx, sqlStr, args...).Scan(&totalUsages)
	if err != nil {
		return nil, err
	}

	// Статистика по типам пользователей
	userTypeStatsQuery := `
		SELECT
			CASE
				WHEN c.expire_at IS NULL OR c.expire_at <= NOW() THEN 'without_subscription'
				ELSE 'with_subscription'
			END as user_type,
			COUNT(*) as count
		FROM promocode_usages pu
		JOIN customer c ON pu.customer_id = c.id
		WHERE pu.promocode_id = $1
		GROUP BY user_type
	`

	rows, err := r.pool.Query(ctx, userTypeStatsQuery, promoCodeID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	userTypeStats := make(map[string]int)
	for rows.Next() {
		var userType string
		var count int
		err := rows.Scan(&userType, &count)
		if err != nil {
			return nil, err
		}
		userTypeStats[userType] = count
	}

	return map[string]interface{}{
		"total_usages":    totalUsages,
		"user_type_stats": userTypeStats,
	}, nil
}
