package payment

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/cache"
	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/cryptopay"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/remnawave"
	"remnawave-tg-shop-bot/internal/service"
	"remnawave-tg-shop-bot/internal/translation"
	"remnawave-tg-shop-bot/internal/yookasa"
	"remnawave-tg-shop-bot/utils"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// AutoRenewalInterface - интерфейс для управления автопродлением
type AutoRenewalInterface interface {
	EnableAutoRenewalAfterPayment(ctx context.Context, customerID int64, paymentMethod string, tariffCode string, paymentMethodData map[string]any) error
}

type PaymentService struct {
	purchaseRepository   *database.PurchaseRepository
	remnawaveClient      *remnawave.Client
	customerRepository   *database.CustomerRepository
	telegramBot          *bot.Bot
	translation          *translation.Manager
	cryptoPayClient      *cryptopay.Client
	yookasaClient        *yookasa.Client
	referralRepository   *database.ReferralRepository
	cache                *cache.Cache
	promoCodeService     *service.PromoCodeService
	tariffRepository     *database.TariffRepository
	autoRenewalInterface AutoRenewalInterface
}

func NewPaymentService(
	translation *translation.Manager,
	purchaseRepository *database.PurchaseRepository,
	remnawaveClient *remnawave.Client,
	customerRepository *database.CustomerRepository,
	telegramBot *bot.Bot,
	cryptoPayClient *cryptopay.Client,
	yookasaClient *yookasa.Client,
	referralRepository *database.ReferralRepository,
	cache *cache.Cache,
	promoCodeService *service.PromoCodeService,
	tariffRepository *database.TariffRepository,
) *PaymentService {
	return &PaymentService{
		purchaseRepository: purchaseRepository,
		remnawaveClient:    remnawaveClient,
		customerRepository: customerRepository,
		telegramBot:        telegramBot,
		translation:        translation,
		cryptoPayClient:    cryptoPayClient,
		yookasaClient:      yookasaClient,
		referralRepository: referralRepository,
		cache:              cache,
		promoCodeService:   promoCodeService,
		tariffRepository:   tariffRepository,
	}
}

// SetAutoRenewalInterface - устанавливает интерфейс для управления автопродлением
func (ps *PaymentService) SetAutoRenewalInterface(autoRenewalInterface AutoRenewalInterface) {
	ps.autoRenewalInterface = autoRenewalInterface
}

func (s PaymentService) ProcessPurchaseById(ctx context.Context, purchaseId int64) error {
	purchase, err := s.purchaseRepository.FindById(ctx, purchaseId)
	if err != nil {
		return err
	}
	if purchase == nil {
		return fmt.Errorf("purchase with crypto invoice id %s not found", utils.MaskHalfInt64(purchaseId))
	}

	customer, err := s.customerRepository.FindById(ctx, purchase.CustomerID)
	if err != nil {
		return err
	}
	if customer == nil {
		return fmt.Errorf("customer %s not found", utils.MaskHalfInt64(purchase.CustomerID))
	}

	if messageId, b := s.cache.GetInt(purchase.ID); b {
		_, err = s.telegramBot.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    customer.TelegramID,
			MessageID: messageId,
		})
		if err != nil {
			slog.Error("Error deleting message", "error", err)
		}
	}

	user, err := s.remnawaveClient.CreateOrUpdateUser(ctx, customer.ID, customer.TelegramID, config.TrafficLimit(), purchase.Month*30)
	if err != nil {
		return err
	}

	err = s.purchaseRepository.MarkAsPaid(ctx, purchase.ID)
	if err != nil {
		return err
	}

	customerFilesToUpdate := map[string]interface{}{
		"subscription_link": user.SubscriptionUrl,
		"expire_at":         user.ExpireAt,
	}

	err = s.customerRepository.UpdateFields(ctx, customer.ID, customerFilesToUpdate)
	if err != nil {
		return err
	}

	// Применяем промокод, если он был указан при создании покупки
	if purchase.PromoCode != nil && *purchase.PromoCode != "" && purchase.TariffCode != nil && *purchase.TariffCode != "" {
		err = s.applyPromoCodeToPurchase(ctx, purchase, customer)
		if err != nil {
			slog.Error("Ошибка применения промокода к оплаченной покупке", "error", err, "purchaseId", purchase.ID, "promoCode", *purchase.PromoCode)
			// Не возвращаем ошибку, так как покупка уже обработана
		}
	}

	// Автоматически включаем автопродление после успешной оплаты
	if purchase.TariffCode != nil && *purchase.TariffCode != "" {
		paymentMethod := s.getPaymentMethodFromPurchase(purchase)
		paymentMethodData := s.extractPaymentMethodData(purchase, paymentMethod)

		err = s.EnableAutoRenewalAfterPayment(ctx, customer.ID, paymentMethod, *purchase.TariffCode, paymentMethodData)
		if err != nil {
			slog.Error("Ошибка включения автопродления после оплаты", "error", err, "purchaseId", purchase.ID, "paymentMethod", paymentMethod)
			// Не возвращаем ошибку, так как покупка уже обработана
		}
	}

	// Создаем улучшенное сообщение с временем окончания подписки
	subscriptionText := s.createSubscriptionActivatedMessage(customer)

	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:    customer.TelegramID,
		Text:      subscriptionText,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: s.createConnectKeyboard(customer),
		},
	})
	if err != nil {
		return err
	}

	ctxReferee := context.Background()
	referee, err := s.referralRepository.FindByReferee(ctxReferee, customer.TelegramID)
	if referee == nil {
		return nil
	}
	if referee.BonusGranted {
		return nil
	}
	if err != nil {
		return err
	}
	refereeCustomer, err := s.customerRepository.FindByTelegramId(ctxReferee, referee.ReferrerID)
	if err != nil {
		return err
	}
	refereeUser, err := s.remnawaveClient.CreateOrUpdateUser(ctxReferee, refereeCustomer.ID, refereeCustomer.TelegramID, config.TrafficLimit(), config.GetReferralDays())
	if err != nil {
		return err
	}
	refereeUserFilesToUpdate := map[string]interface{}{
		"subscription_link": refereeUser.GetSubscriptionUrl(),
		"expire_at":         refereeUser.GetExpireAt(),
	}
	err = s.customerRepository.UpdateFields(ctxReferee, refereeCustomer.ID, refereeUserFilesToUpdate)
	if err != nil {
		return err
	}
	err = s.referralRepository.MarkBonusGranted(ctxReferee, referee.ID)
	if err != nil {
		return err
	}
	slog.Info("Granted referral bonus", "customer_id", utils.MaskHalfInt64(refereeCustomer.ID))
	_, err = s.telegramBot.SendMessage(ctxReferee, &bot.SendMessageParams{
		ChatID:    refereeCustomer.TelegramID,
		ParseMode: models.ParseModeHTML,
		Text:      s.translation.GetText(refereeCustomer.Language, "referral_bonus_granted"),
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: s.createConnectKeyboard(refereeCustomer),
		},
	})
	slog.Info("purchase processed", "purchase_id", utils.MaskHalfInt64(purchase.ID), "type", purchase.InvoiceType, "customer_id", utils.MaskHalfInt64(customer.ID))

	return nil
}

func (s PaymentService) createConnectKeyboard(customer *database.Customer) [][]models.InlineKeyboardButton {
	var inlineCustomerKeyboard [][]models.InlineKeyboardButton
	var url string
	if customer != nil && customer.SubscriptionLink != nil && *customer.SubscriptionLink != "" {
		url = *customer.SubscriptionLink
	} else if config.GetMiniAppURL() != "" {
		url = config.GetMiniAppURL()
	}
	if url != "" {
		inlineCustomerKeyboard = append(inlineCustomerKeyboard, []models.InlineKeyboardButton{
			{Text: s.translation.GetText(customer.Language, "connect_button"), WebApp: &models.WebAppInfo{URL: url}},
		})
	} else {
		inlineCustomerKeyboard = append(inlineCustomerKeyboard, []models.InlineKeyboardButton{
			{Text: s.translation.GetText(customer.Language, "connect_button"), CallbackData: "connect"},
		})
	}
	inlineCustomerKeyboard = append(inlineCustomerKeyboard, []models.InlineKeyboardButton{
		{Text: s.translation.GetText(customer.Language, "back_button"), CallbackData: "start"},
	})
	return inlineCustomerKeyboard
}

// createSubscriptionActivatedMessage - создает сообщение об активации подписки с временем окончания
func (s PaymentService) createSubscriptionActivatedMessage(customer *database.Customer) string {
	baseMessage := s.translation.GetText(customer.Language, "subscription_activated")

	// Добавляем информацию о времени окончания подписки, если она есть
	if customer.ExpireAt != nil {
		// Используем правильное форматирование времени как в системе рассылок
		loc, err := time.LoadLocation(config.TimeZone())
		if err != nil {
			loc = time.FixedZone("UTC+3", 3*60*60) // fallback
		}

		expireTimeFormatted := customer.ExpireAt.In(loc)
		_, offset := expireTimeFormatted.Zone()
		hours := offset / 3600
		var sign string
		if hours >= 0 {
			sign = "+"
		} else {
			sign = "-"
		}

		timeStr := expireTimeFormatted.Format("02.01.2006 15:04") +
			fmt.Sprintf(" (UTC%s%02d)", sign, abs(hours))

		baseMessage += fmt.Sprintf("\n\n⏰ <b>Подписка действует до:</b> %s", timeStr)
	}

	return baseMessage
}

// abs - вспомогательная функция для получения абсолютного значения
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func (s PaymentService) CreatePurchase(ctx context.Context, amount int, months int, customer *database.Customer, invoiceType database.InvoiceType) (url string, purchaseId int64, err error) {
	// Метод CreatePurchase не поддерживает промокоды и тарифы, передаем пустые строки
	switch invoiceType {
	case database.InvoiceTypeCrypto:
		return s.createCryptoInvoice(ctx, amount, months, customer, "", "")
	case database.InvoiceTypeYookasa:
		return s.createYookasaInvoice(ctx, amount, months, customer, "", "")
	case database.InvoiceTypeTelegram:
		return s.createTelegramInvoice(ctx, amount, months, customer, "", "")
	case database.InvoiceTypeTribute:
		return s.createTributeInvoice(ctx, amount, months, customer, "", "")
	default:
		return "", 0, fmt.Errorf("unknown invoice type: %s", invoiceType)
	}
}

func (s PaymentService) CreatePurchaseByTariff(ctx context.Context, tariff *database.Tariff, customer *database.Customer, invoiceType database.InvoiceType, promoCode string) (url string, purchaseId int64, err error) {
	var amount int
	// Извлекаем количество месяцев из кода тарифа
	months := tariff.GetMonthsFromCode()

	// Определяем базовую сумму в зависимости от типа оплаты
	var originalAmount int
	var currency string
	switch invoiceType {
	case database.InvoiceTypeTelegram:
		originalAmount = tariff.PriceStars
		currency = "STARS"
	case database.InvoiceTypeYookasa, database.InvoiceTypeCrypto, database.InvoiceTypeTribute:
		originalAmount = tariff.PriceRUB
		currency = "RUB"
	default:
		return "", 0, fmt.Errorf("неизвестный тип оплаты: %s", invoiceType)
	}

	// Применяем промокод, если он указан
	amount = originalAmount
	var validPromoCode *database.PromoCode
	if promoCode != "" && s.promoCodeService != nil {
		// Валидируем промокод
		validatedPromoCode, err := s.promoCodeService.ValidatePromoCode(ctx, promoCode, customer, tariff)
		if err != nil {
			slog.Error("Ошибка валидации промокода", "error", err, "code", promoCode)
			// Продолжаем без промокода, если валидация не прошла
		} else {
			validPromoCode = validatedPromoCode
			// Рассчитываем цену со скидкой
			_, amount = s.promoCodeService.CalculateDiscountedPrice(originalAmount, validPromoCode.DiscountPercent, currency)
		}
	}

	switch invoiceType {
	case database.InvoiceTypeCrypto:
		url, purchaseId, err = s.createCryptoInvoice(ctx, amount, months, customer, promoCode, tariff.Code)
	case database.InvoiceTypeYookasa:
		url, purchaseId, err = s.createYookasaInvoice(ctx, amount, months, customer, promoCode, tariff.Code)
	case database.InvoiceTypeTelegram:
		url, purchaseId, err = s.createTelegramInvoice(ctx, amount, months, customer, promoCode, tariff.Code)
	case database.InvoiceTypeTribute:
		url, purchaseId, err = s.createTributeInvoice(ctx, amount, months, customer, promoCode, tariff.Code)
	default:
		return "", 0, fmt.Errorf("неизвестный тип оплаты: %s", invoiceType)
	}

	if err != nil {
		return "", 0, err
	}

	// TODO: Применение промокода должно происходить только после успешной оплаты
	// Информация о промокоде должна сохраняться в purchase или передаваться через payload

	return url, purchaseId, nil
}

func (s PaymentService) createCryptoInvoice(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeCrypto,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
	})
	if err != nil {
		slog.Error("Error creating purchase", "error", err)
		return "", 0, err
	}

	invoice, err := s.cryptoPayClient.CreateInvoice(&cryptopay.InvoiceRequest{
		CurrencyType:   "fiat",
		Fiat:           "RUB",
		Amount:         fmt.Sprintf("%d", amount),
		AcceptedAssets: "USDT",
		Payload:        fmt.Sprintf("purchaseId=%d&username=%s", purchaseId, ctx.Value("username")),
		Description: fmt.Sprintf("Subscription on %d month%s", months, func() string {
			if months > 1 {
				return "s"
			} else {
				return ""
			}
		}()),
		PaidBtnName: "callback",
		PaidBtnUrl:  config.BotURL(),
	})
	if err != nil {
		slog.Error("Error creating invoice", "error", err)
		return "", 0, err
	}

	updates := map[string]interface{}{
		"crypto_invoice_url": invoice.BotInvoiceUrl,
		"crypto_invoice_id":  invoice.InvoiceID,
		"status":             database.PurchaseStatusPending,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, updates)
	if err != nil {
		slog.Error("Error updating purchase", "error", err)
		return "", 0, err
	}

	return invoice.BotInvoiceUrl, purchaseId, nil
}

func (s PaymentService) createYookasaInvoice(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeYookasa,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
	})
	if err != nil {
		slog.Error("Error creating purchase", "error", err)
		return "", 0, err
	}

	invoice, err := s.yookasaClient.CreateInvoice(ctx, amount, months, customer.ID, purchaseId)
	if err != nil {
		slog.Error("Error creating invoice", "error", err)
		return "", 0, err
	}

	updates := map[string]interface{}{
		"yookasa_url": invoice.Confirmation.ConfirmationURL,
		"yookasa_id":  invoice.ID,
		"status":      database.PurchaseStatusPending,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, updates)
	if err != nil {
		slog.Error("Error updating purchase", "error", err)
		return "", 0, err
	}

	return invoice.Confirmation.ConfirmationURL, purchaseId, nil
}

func (s PaymentService) createTelegramInvoice(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeTelegram,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "STARS",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
	})
	if err != nil {
		slog.Error("Error creating purchase", "error", err)
		return "", 0, nil
	}

	invoiceUrl, err := s.telegramBot.CreateInvoiceLink(ctx, &bot.CreateInvoiceLinkParams{
		Title:    s.translation.GetText(customer.Language, "invoice_title"),
		Currency: "XTR",
		Prices: []models.LabeledPrice{
			{
				Label:  s.translation.GetText(customer.Language, "invoice_label"),
				Amount: amount,
			},
		},
		Description: s.translation.GetText(customer.Language, "invoice_description"),
		Payload:     fmt.Sprintf("%d&%s", purchaseId, ctx.Value("username")),
	})

	updates := map[string]interface{}{
		"status": database.PurchaseStatusPending,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, updates)
	if err != nil {
		slog.Error("Error updating purchase", "error", err)
		return "", 0, err
	}

	return invoiceUrl, purchaseId, nil
}

func (s PaymentService) ActivateTrial(ctx context.Context, telegramId int64) (string, error) {
	if config.TrialDays() == 0 {
		return "", nil
	}
	customer, err := s.customerRepository.FindByTelegramId(ctx, telegramId)
	if err != nil {
		slog.Error("Error finding customer", "error", err)
		return "", err
	}
	if customer == nil {
		return "", fmt.Errorf("customer %d not found", telegramId)
	}
	user, err := s.remnawaveClient.CreateOrUpdateUser(ctx, customer.ID, telegramId, config.TrialTrafficLimit(), config.TrialDays())
	if err != nil {
		slog.Error("Error creating user", "error", err)
		return "", err
	}

	customerFilesToUpdate := map[string]interface{}{
		"subscription_link": user.GetSubscriptionUrl(),
		"expire_at":         user.GetExpireAt(),
	}

	err = s.customerRepository.UpdateFields(ctx, customer.ID, customerFilesToUpdate)
	if err != nil {
		return "", err
	}

	return user.GetSubscriptionUrl(), nil

}

func (s PaymentService) CancelPayment(purchaseId int64) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	purchase, err := s.purchaseRepository.FindById(ctx, purchaseId)
	if err != nil {
		return err
	}
	if purchase == nil {
		return fmt.Errorf("purchase with crypto invoice id %s not found", utils.MaskHalfInt64(purchaseId))
	}

	purchaseFieldsToUpdate := map[string]interface{}{
		"status": database.PurchaseStatusCancel,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, purchaseFieldsToUpdate)
	if err != nil {
		return err
	}

	return nil
}

// applyPromoCodeToPurchase применяет промокод к оплаченной покупке
func (s PaymentService) applyPromoCodeToPurchase(ctx context.Context, purchase *database.Purchase, customer *database.Customer) error {
	if s.promoCodeService == nil {
		return fmt.Errorf("promoCodeService не инициализирован")
	}

	// Получаем промокод из базы данных
	promoCode, err := s.promoCodeService.GetPromoCodeByCode(ctx, *purchase.PromoCode)
	if err != nil {
		return fmt.Errorf("ошибка получения промокода: %w", err)
	}
	if promoCode == nil {
		return fmt.Errorf("промокод %s не найден", *purchase.PromoCode)
	}

	// Получаем тариф из базы данных
	tariff, err := s.tariffRepository.GetByCode(ctx, *purchase.TariffCode)
	if err != nil {
		return fmt.Errorf("ошибка получения тарифа: %w", err)
	}
	if tariff == nil {
		return fmt.Errorf("тариф %s не найден", *purchase.TariffCode)
	}

	// Определяем валюту на основе типа инвойса
	var currency string
	switch purchase.InvoiceType {
	case database.InvoiceTypeTelegram:
		currency = "STARS"
	case database.InvoiceTypeYookasa, database.InvoiceTypeCrypto, database.InvoiceTypeTribute:
		currency = "RUB"
	default:
		return fmt.Errorf("неизвестный тип инвойса: %s", purchase.InvoiceType)
	}

	// Применяем промокод
	_, err = s.promoCodeService.ApplyPromoCode(ctx, promoCode, customer, tariff, purchase.ID, currency)
	if err != nil {
		return fmt.Errorf("ошибка применения промокода: %w", err)
	}

	slog.Info("Промокод успешно применен к покупке", "purchaseId", purchase.ID, "promoCode", *purchase.PromoCode, "customerId", customer.ID)
	return nil
}

func (s PaymentService) createTributeInvoice(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeTribute,
		Status:      database.PurchaseStatusPending,
		Amount:      float64(amount),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
	})
	if err != nil {
		slog.Error("Error creating purchase", "error", err)
		return "", 0, err
	}

	return "", purchaseId, nil
}

// CreateAutoRenewalPayment - создает автоматический платеж для продления подписки
func (ps *PaymentService) CreateAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff, paymentMethod string, savedPaymentData map[string]any) (int64, error) {
	switch paymentMethod {
	case "yookasa":
		return ps.createYookasaAutoRenewalPayment(ctx, customer, tariff, savedPaymentData)
	case "cryptopay":
		return ps.createCryptoPayAutoRenewalPayment(ctx, customer, tariff)
	case "telegram":
		return ps.createTelegramAutoRenewalPayment(ctx, customer, tariff)
	case "tribute":
		return ps.createTributeAutoRenewalPayment(ctx, customer, tariff)
	default:
		return 0, fmt.Errorf("unsupported payment method for auto renewal: %s", paymentMethod)
	}
}

// EnableAutoRenewalAfterPayment - включает автопродление после успешной оплаты
func (ps *PaymentService) EnableAutoRenewalAfterPayment(ctx context.Context, customerID int64, paymentMethod string, tariffCode string, paymentMethodData map[string]any) error {
	// Создаем интерфейс для автопродления через dependency injection
	if ps.autoRenewalInterface != nil {
		err := ps.autoRenewalInterface.EnableAutoRenewalAfterPayment(ctx, customerID, paymentMethod, tariffCode, paymentMethodData)
		if err != nil {
			slog.Error("Failed to enable auto renewal after payment", "error", err, "customer_id", customerID)
			return err
		}

		slog.Info("Auto renewal enabled successfully after payment",
			"customer_id", customerID,
			"payment_method", paymentMethod,
			"tariff_code", tariffCode)
		return nil
	}

	// Fallback: логируем, если интерфейс не настроен
	slog.Warn("Auto renewal interface not configured, skipping auto renewal setup",
		"customer_id", customerID,
		"payment_method", paymentMethod,
		"tariff_code", tariffCode)
	return nil
}

// createAutoRenewalPurchase - создает покупку для автопродления без применения промокодов
func (s PaymentService) createAutoRenewalPurchase(ctx context.Context, tariff *database.Tariff, customer *database.Customer, invoiceType database.InvoiceType) (url string, purchaseId int64, err error) {
	// Извлекаем количество месяцев из кода тарифа
	months := tariff.GetMonthsFromCode()

	// Определяем полную стоимость тарифа БЕЗ применения промокодов
	var amount int
	switch invoiceType {
	case database.InvoiceTypeTelegram:
		amount = tariff.PriceStars
	case database.InvoiceTypeYookasa, database.InvoiceTypeCrypto, database.InvoiceTypeTribute:
		amount = tariff.PriceRUB
	default:
		return "", 0, fmt.Errorf("неизвестный тип оплаты: %s", invoiceType)
	}

	// Создаем покупку с полной стоимостью (без промокода)
	switch invoiceType {
	case database.InvoiceTypeCrypto:
		return s.createCryptoInvoice(ctx, amount, months, customer, "", tariff.Code)
	case database.InvoiceTypeYookasa:
		return s.createYookasaInvoice(ctx, amount, months, customer, "", tariff.Code)
	case database.InvoiceTypeTelegram:
		return s.createTelegramInvoice(ctx, amount, months, customer, "", tariff.Code)
	case database.InvoiceTypeTribute:
		return s.createTributeInvoice(ctx, amount, months, customer, "", tariff.Code)
	default:
		return "", 0, fmt.Errorf("неподдерживаемый тип инвойса для автопродления: %s", invoiceType)
	}
}

// createYookasaAutoRenewalPayment - создает автоматический платеж через Yookasa
func (ps *PaymentService) createYookasaAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff, savedPaymentData map[string]any) (int64, error) {
	// Получаем payment_method_id из сохраненных данных
	paymentMethodID, ok := savedPaymentData["payment_method_id"].(string)
	if !ok || paymentMethodID == "" {
		// Если нет сохраненного способа оплаты, создаем обычный инвойс с сохранением
		_, purchaseID, err := ps.createAutoRenewalPurchase(ctx, tariff, customer, database.InvoiceTypeYookasa)
		return purchaseID, err
	}

	// TODO: Использовать сохраненный способ оплаты через Yookasa API
	// Пока что создаем обычный инвойс
	_, purchaseID, err := ps.createAutoRenewalPurchase(ctx, tariff, customer, database.InvoiceTypeYookasa)
	return purchaseID, err
}

// createCryptoPayAutoRenewalPayment - создает автоматический платеж через CryptoPay
func (ps *PaymentService) createCryptoPayAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff) (int64, error) {
	// Для CryptoPay создаем инвойс без промокодов и отправляем уведомление пользователю
	_, purchaseID, err := ps.createAutoRenewalPurchase(ctx, tariff, customer, database.InvoiceTypeCrypto)
	if err != nil {
		return 0, fmt.Errorf("failed to create crypto pay purchase: %w", err)
	}

	// Отправляем уведомление пользователю о необходимости оплаты
	ps.sendAutoRenewalCryptoPayNotification(ctx, customer, tariff, purchaseID)

	slog.Info("CryptoPay auto renewal invoice created and notification sent",
		"customer_id", customer.ID,
		"purchase_id", purchaseID,
		"tariff_code", tariff.Code)

	return purchaseID, nil
}

// createTelegramAutoRenewalPayment - создает автоматический платеж через Telegram Stars
func (ps *PaymentService) createTelegramAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff) (int64, error) {
	// Для Telegram Stars создаем инвойс без промокодов
	_, purchaseID, err := ps.createAutoRenewalPurchase(ctx, tariff, customer, database.InvoiceTypeTelegram)
	if err != nil {
		return 0, fmt.Errorf("failed to create telegram purchase: %w", err)
	}

	// Отправляем уведомление пользователю о необходимости оплаты
	ps.sendAutoRenewalTelegramNotification(ctx, customer, tariff, purchaseID)

	slog.Info("Telegram Stars auto renewal invoice created and notification sent",
		"customer_id", customer.ID,
		"purchase_id", purchaseID,
		"tariff_code", tariff.Code)

	return purchaseID, nil
}

// createTributeAutoRenewalPayment - создает автоматический платеж через Tribute
func (ps *PaymentService) createTributeAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff) (int64, error) {
	// Tribute работает через подписки, которые автоматически продлеваются
	// Поэтому для Tribute мы не создаем новые инвойсы, а полагаемся на их систему автопродления

	// Отправляем уведомление пользователю о том, что автопродление работает через Tribute
	ps.sendAutoRenewalTributeNotification(ctx, customer, tariff)

	// Возвращаем 0 как purchase_id, так как реальная покупка будет создана через webhook
	slog.Info("Tribute auto renewal notification sent - renewal handled by Tribute system",
		"customer_id", customer.ID,
		"tariff_code", tariff.Code)

	return 0, nil
}

// sendAutoRenewalCryptoPayNotification - отправляет уведомление об автопродлении через CryptoPay
func (ps *PaymentService) sendAutoRenewalCryptoPayNotification(ctx context.Context, customer *database.Customer, tariff *database.Tariff, purchaseID int64) {
	langCode := customer.Language
	if langCode == "" {
		langCode = "ru"
	}

	// Получаем информацию о покупке
	purchase, err := ps.purchaseRepository.FindById(ctx, purchaseID)
	if err != nil {
		slog.Error("Failed to get purchase for crypto notification", "error", err, "purchase_id", purchaseID)
		return
	}

	messageText := fmt.Sprintf(
		"🔄 <b>Автопродление подписки</b>\n\n"+
			"📦 Тариф: <b>%s</b>\n"+
			"💰 Стоимость: <b>%d₽</b>\n"+
			"💳 Способ оплаты: <b>Криптовалюта</b>\n"+
			"⏰ Срок действия: <b>%d мес.</b>\n\n"+
			"Для продления подписки нажмите кнопку ниже и оплатите через CryptoPay:",
		tariff.Title,
		tariff.PriceRUB,
		purchase.Month,
	)

	// Получаем URL инвойса из покупки
	var invoiceURL string
	if purchase.CryptoInvoiceLink != nil {
		invoiceURL = *purchase.CryptoInvoiceLink
	}

	keyboard := models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text: "₿ Оплатить через CryptoPay",
					URL:  invoiceURL,
				},
			},
			{
				{
					Text:         "❌ Отменить автопродление",
					CallbackData: "auto_renewal_disable",
				},
			},
		},
	}

	_, err = ps.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Failed to send auto renewal crypto pay notification", "error", err)
	} else {
		slog.Info("Auto renewal crypto pay notification sent successfully",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID,
			"purchase_id", purchaseID)
	}
}

// sendAutoRenewalTelegramNotification - отправляет уведомление об автопродлении через Telegram Stars
func (ps *PaymentService) sendAutoRenewalTelegramNotification(ctx context.Context, customer *database.Customer, tariff *database.Tariff, purchaseID int64) {
	langCode := customer.Language
	if langCode == "" {
		langCode = "ru"
	}

	// Получаем информацию о покупке для создания инвойса
	purchase, err := ps.purchaseRepository.FindById(ctx, purchaseID)
	if err != nil {
		slog.Error("Failed to get purchase for telegram notification", "error", err, "purchase_id", purchaseID)
		return
	}

	messageText := fmt.Sprintf(
		"🔄 <b>Автопродление подписки</b>\n\n"+
			"📦 Тариф: <b>%s</b>\n"+
			"💰 Стоимость: <b>%d⭐</b>\n"+
			"💳 Способ оплаты: <b>Telegram Stars</b>\n"+
			"⏰ Срок действия: <b>%d мес.</b>\n\n"+
			"Для продления подписки нажмите кнопку ниже и оплатите через Telegram Stars:",
		tariff.Title,
		tariff.PriceStars,
		purchase.Month,
	)

	// Создаем инвойс для Telegram Stars
	invoiceURL, err := ps.createTelegramInvoiceForAutoRenewal(ctx, customer, tariff, purchaseID)
	if err != nil {
		slog.Error("Failed to create telegram invoice for auto renewal", "error", err)
		return
	}

	keyboard := models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text: "⭐ Оплатить через Telegram Stars",
					URL:  invoiceURL,
				},
			},
			{
				{
					Text:         "❌ Отменить автопродление",
					CallbackData: "auto_renewal_disable",
				},
			},
		},
	}

	_, err = ps.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Failed to send auto renewal telegram notification", "error", err)
	} else {
		slog.Info("Auto renewal telegram notification sent successfully",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID,
			"purchase_id", purchaseID)
	}
}

// getPaymentMethodFromPurchase - определяет способ оплаты по данным покупки
func (ps *PaymentService) getPaymentMethodFromPurchase(purchase *database.Purchase) string {
	// Используем InvoiceType для определения способа оплаты
	switch purchase.InvoiceType {
	case database.InvoiceTypeYookasa:
		return "yookasa"
	case database.InvoiceTypeCrypto:
		return "cryptopay"
	case database.InvoiceTypeTelegram:
		return "telegram"
	case database.InvoiceTypeTribute:
		return "tribute"
	default:
		return "unknown"
	}
}

// extractPaymentMethodData - извлекает данные способа оплаты для сохранения
func (ps *PaymentService) extractPaymentMethodData(purchase *database.Purchase, paymentMethod string) map[string]any {
	data := make(map[string]any)

	switch paymentMethod {
	case "yookasa":
		if purchase.YookasaID != nil {
			data["yookasa_id"] = purchase.YookasaID.String()
			// TODO: Получить payment_method_id из Yookasa API если платеж был с сохранением
		}
	case "cryptopay":
		if purchase.CryptoInvoiceID != nil {
			data["crypto_invoice_id"] = *purchase.CryptoInvoiceID
		}
	case "telegram":
		// Для Telegram Stars пока что сохраняем только тип
		data["payment_type"] = "telegram_stars"
	case "tribute":
		// Для Tribute пока что сохраняем только тип
		data["payment_type"] = "tribute"
	}

	return data
}

// createTelegramInvoiceForAutoRenewal - создает инвойс Telegram Stars для автопродления
func (ps *PaymentService) createTelegramInvoiceForAutoRenewal(ctx context.Context, customer *database.Customer, tariff *database.Tariff, purchaseID int64) (string, error) {
	// Получаем информацию о покупке
	purchase, err := ps.purchaseRepository.FindById(ctx, purchaseID)
	if err != nil {
		return "", fmt.Errorf("failed to get purchase: %w", err)
	}

	// Создаем инвойс через Telegram Bot API
	invoiceURL, err := ps.telegramBot.CreateInvoiceLink(ctx, &bot.CreateInvoiceLinkParams{
		Title:    ps.translation.GetText(customer.Language, "auto_renewal_invoice_title"),
		Currency: "XTR",
		Prices: []models.LabeledPrice{
			{
				Label:  fmt.Sprintf("%s (%d мес.)", tariff.Title, purchase.Month),
				Amount: tariff.PriceStars,
			},
		},
		Description: fmt.Sprintf(
			ps.translation.GetText(customer.Language, "auto_renewal_invoice_description"),
			tariff.Title,
		),
		Payload: fmt.Sprintf("%d&%s", purchaseID, "auto_renewal"),
	})

	if err != nil {
		return "", fmt.Errorf("failed to create telegram invoice: %w", err)
	}

	return invoiceURL, nil
}

// sendAutoRenewalTributeNotification - отправляет уведомление об автопродлении через Tribute
func (ps *PaymentService) sendAutoRenewalTributeNotification(ctx context.Context, customer *database.Customer, tariff *database.Tariff) {
	langCode := customer.Language
	if langCode == "" {
		langCode = "ru"
	}

	messageText := fmt.Sprintf(
		"🔄 <b>Автопродление подписки</b>\n\n"+
			"📦 Тариф: <b>%s</b>\n"+
			"💰 Стоимость: <b>%d₽</b>\n"+
			"💳 Способ оплаты: <b>Tribute</b>\n\n"+
			"✅ <b>Автопродление активно!</b>\n\n"+
			"Ваша подписка будет автоматически продлена через систему Tribute. "+
			"Никаких дополнительных действий не требуется.\n\n"+
			"Если вы хотите отменить автопродление, сделайте это в настройках Tribute.",
		tariff.Title,
		tariff.PriceRUB,
	)

	keyboard := models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{
			{
				{
					Text: "🏛️ Управление подпиской",
					URL:  config.GetTributePaymentUrl(),
				},
			},
			{
				{
					Text:         "❌ Отключить автопродление",
					CallbackData: "auto_renewal_disable",
				},
			},
		},
	}

	_, err := ps.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      customer.TelegramID,
		Text:        messageText,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})

	if err != nil {
		slog.Error("Failed to send auto renewal tribute notification", "error", err)
	} else {
		slog.Info("Auto renewal tribute notification sent successfully",
			"customer_id", customer.ID,
			"telegram_id", customer.TelegramID)
	}
}
