package interfaces

import (
	"context"
	"remnawave-tg-shop-bot/internal/database"
)

// AutoRenewalPaymentInterface - интерфейс для создания автоматических платежей
type AutoRenewalPaymentInterface interface {
	// CreateAutoRenewalPayment - создает автоматический платеж для продления подписки
	CreateAutoRenewalPayment(ctx context.Context, customer *database.Customer, tariff *database.Tariff, paymentMethod string, savedPaymentData map[string]any) (int64, error)

	// EnableAutoRenewalAfterPayment - включает автопродление после успешной оплаты
	EnableAutoRenewalAfterPayment(ctx context.Context, customerID int64, paymentMethod string, tariffCode string, paymentMethodData map[string]any) error
}

// AutoRenewalServiceInterface - интерфейс для управления автопродлением
type AutoRenewalServiceInterface interface {
	// EnableAutoRenewal - включает автопродление для пользователя
	EnableAutoRenewal(ctx context.Context, customerID int64, paymentMethod string, tariffCode string, savedPaymentData map[string]any) error
}
